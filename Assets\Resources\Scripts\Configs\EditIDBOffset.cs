using TMPro;
using UnityEngine;

public class EditIdbOffset : MonoBehaviour
{
    private ConfigsHandler configsHandler; // Reference to the ConfigsHandler
    private TMP_InputField text;

    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script

        text = GetComponent<TMP_InputField>(); // Get the TMP_InputField component

        text.text = configsHandler.IDBOffset.ToString();

        text.onEndEdit.AddListener(EditValue); // Add the listener to the TMP_InputField to edit the value
    }


    void EditValue(string arg) // Edit the value of the energy timer
    {
        configsHandler.IDBOffset = float.Parse(arg); // Set the energy timer to the new value
        configsHandler.SaveValues(); // Save the value
    }
}
