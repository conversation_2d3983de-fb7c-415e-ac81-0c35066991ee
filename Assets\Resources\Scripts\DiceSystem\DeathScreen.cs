using System;
using UnityEngine;
using UnityEngine.UI;

public class DeathScreen : MonoBehaviour
{
    public Button Death, DiceSystem;
    ConfigsHandler configsHandler;
    GridManager gridManager;
    public GameObject settingsButton;

    private void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();
        gridManager = GameObject.Find("GridManager").GetComponent<GridManager>();

        Death.onClick.AddListener(delegate { DeathRoutine(); });
        DiceSystem.onClick.AddListener(delegate { transform.parent.GetChild(1).gameObject.SetActive(true); gameObject.SetActive(false); });
    }

    void DeathRoutine()
    {
        gridManager.ReviveRunes();
        gameObject.SetActive(false);
        configsHandler.win = false;

    }
}
