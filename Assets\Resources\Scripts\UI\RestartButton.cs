using UnityEngine;
using UnityEngine.UI;

public class RestartButton : MonoBehaviour
{
    ConfigsHandler configsHandler;
    void Start() // Add the listener to the button to restart the game
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();

        GetComponent<Button>().onClick.AddListener(() =>
        {
            // Save the values to the file
            StartCoroutine(configsHandler.LoadedValues.Save());

            new ReloadGame();
        });
    }
}
