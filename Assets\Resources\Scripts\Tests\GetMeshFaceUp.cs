using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using TMPro;
using UnityEngine.InputSystem;
using System.Collections;

[System.Serializable]
public class DiceSide
{
    public Vector3 Center;
    public Vector3 Normal;
    public int Value;
}


public class GetMeshFaceUp : MonoBehaviour
{
    public DiceSide[] Sides;

    public Camera diceCamera;

    ConfigsHandler configsHandler;

    public enum EdgeDirection
    {
        Edge1 = 1,
        Edge2 = 2,
        Edge3 = 3
    }

    // wich edge the text will be aligned to
    readonly int[] egdes = { 2, 3, 3, 3, 3, 1, 3, 2, 2, 2, 2, 2, 2, 1, 2, 1, 1, 1, 1, 2 };
    // degrees that the dice needs to stay in order to align the text
    //                         1    2    3    4     5   6   7    8     9    10    11   12   13   14   15  16  17  18 19   20
    readonly int[] angles = { -60, 180, 120, 120, -180, 0, 120, -60, -120, -120, -60, -60, -120, 60, -180, 0, 60, 60, 0, -120 };
    Vector3 startPos;

    // it counts the number of frames that the dice is not moving
    int counter = 0;

    int collisionCount;

    // it stores the number of the face that is pointing up
    public int diceSide = -1;

    // modifier for the speed and rotation of the dice
    public float speedMultiplier = 8f, torqueMultiplier = 10f;

    public MeshCollider meshCollider;
    public GameObject textPrefab; // Prefab do objeto de texto 3D
    private Color[] originalColors;
    private Dictionary<int, int> faceIndexToNumber;
    readonly private List<GameObject> textObjects = new();
    public float textOffset = 0.1f; // Deslocamento do texto acima da face

    GameObject wall1;
    GameObject wall2;
    GameObject wall3;
    GameObject wall4;

    int rollCount = 0;

    Rigidbody diceRb;

    Mesh mesh;
    List<int> largestFaces = new();

    bool diceMovementLow;

    void Start()
    {

        // Gets the configs handler
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();

        wall1 = GameObject.Find("wall1");
        wall2 = GameObject.Find("wall2");
        wall3 = GameObject.Find("wall3");
        wall4 = GameObject.Find("wall4");

        // saves the start position of the dice
        startPos = transform.position;

        // gets the Rigidbody and mesh collider of the dice
        diceRb = GetComponent<Rigidbody>();

        if (meshCollider == null)
        {
            meshCollider = GetComponent<MeshCollider>();
        }

        // stores the mesh of the dice and the original colors of the vertices
        mesh = meshCollider.sharedMesh;
        originalColors = new Color[mesh.vertexCount];
        if (mesh.colors.Length > 0)
        {
            originalColors = mesh.colors;
        }
        else
        {
            for (int i = 0; i < originalColors.Length; i++)
            {
                originalColors[i] = Color.white;
            }
        }

        // Calculates the first 20 biggest faces of the mesh
        largestFaces = GetLargestFaces(mesh, 20);
        // the numbers of each face are stored in the faceIndexToNumber dictionary
        int[] faceNumbers = { 1, 17, 4, 20, 11, 8, 13, 10, 3, 18, 2, 19, 14, 7, 9, 5, 16, 12, 15, 6 };
        faceIndexToNumber = new Dictionary<int, int>();
        for (int i = 0; i < largestFaces.Count; i++)
        {
            faceIndexToNumber[largestFaces[i]] = faceNumbers[i];
        }

        // Adds the text to the faces of the dice
        AddTextToFaces(mesh, largestFaces);
    }

    void Update()
    {
        diceCamera.enabled = configsHandler.FailPopup.transform.GetChild(1).gameObject.activeSelf && configsHandler.FailPopup.activeSelf; // disable the camera when the popup is not active

        // If the dice is not moving, it checks which face is pointing up
        int faceNumber = GetFacePointingUp();
        //Debug.Log("Face Number: " + faceNumber);

        // Makes sure the dice falls on a flat face
        diceMovementLow = IsDiceMovementLow(diceRb);
        if (diceMovementLow && collisionCount == 0)
        {
            wall1.SetActive(true);
            wall2.SetActive(true);
            wall3.SetActive(true);
            wall4.SetActive(true);
        }
        else if (diceMovementLow)
        {
            wall1.SetActive(false);
            wall2.SetActive(false);
            wall3.SetActive(false);
            wall4.SetActive(false);
        }
        else
        {
            wall1.SetActive(true);
            wall2.SetActive(true);
            wall3.SetActive(true);
            wall4.SetActive(true);
        }

        // If the dice is not moving, it moves the dice to the start position
        if (faceNumber != -1 && diceRb.linearVelocity.magnitude < 0.0001f && diceRb.angularVelocity.magnitude < 0.0001f)
        {
            // waits 10 frames to check before moving the dice
            if (counter < 10)
                counter++;
            else
            {
                // if the dice is not moving, aligns the edge to the bottom
                if (diceSide == -1 || diceRb.isKinematic) diceSide = faceNumber;

                diceRb.isKinematic = true;

                //Debug.Log("Face pointing up: " + diceSide + " Edge number: " + egdes[diceSide - 1]);
                AlignEdgeToBottom(diceSide);
            }
        }
        else counter = 0;

    }

    private void OnCollisionEnter()
    {
        if (diceCamera.enabled)
        {
            collisionCount++;
            //Debug.Log("Collision count: " + collisionCount);

            // Play sound effect
            ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/Dice"));

            //// Calculate the opposite direction of the previous collision
            Vector3 force = new Vector3(Random.Range(0f, 1f), 0f, Random.Range(0f, 1f));

            if (collisionCount <= 1)
            {
                diceRb.AddForce(force * (speedMultiplier * 1), ForceMode.Impulse);
                diceRb.AddTorque(Random.insideUnitSphere * (torqueMultiplier * 3), ForceMode.Impulse);
            }
            if (collisionCount >= 2)
            {
                Time.timeScale = 7f; // Speeds up time
            }

        }
    }
    // checks if the dice is not moving and is kinematic
    public bool CanRollTheDice()
    {
        return diceRb.isKinematic && Vector3.Distance(transform.position, startPos) < 0.1f;
    }

    // rolls the dice
    public void RollTheDice()
    {
        wall1.SetActive(true);
        wall2.SetActive(true);
        wall3.SetActive(true);
        wall4.SetActive(true);
        collisionCount = 0; // resets the collision count

        diceRb.isKinematic = false;
        // Creates a random direction for the dice to roll
        Vector3 randomDirection = new(Random.Range(-1f, 1f), -0f, Random.Range(-1f, 1f));


        // normalizes the direction
        randomDirection = randomDirection.normalized;

        // applies a random torque to the dice
        diceRb.AddForce(new Vector3(randomDirection.x, -0.5f, randomDirection.z) * (speedMultiplier * 2), ForceMode.Impulse);
        diceRb.AddTorque(Random.insideUnitSphere * (torqueMultiplier * 6), ForceMode.Impulse);
        counter = 0; // resets the counter
        rollCount++;

    }

    int GetFacePointingUp()
    {
        Vector3[] normals = mesh.normals;
        int[] triangles = mesh.triangles;

        float maxDotProduct = -1;
        int faceIndex = -1;


        for (int i = 0; i < largestFaces.Count; i++)
        {
            int face = largestFaces[i];
            Vector3 normal = (normals[triangles[face * 3]] + normals[triangles[face * 3 + 1]] + normals[triangles[face * 3 + 2]]) / 3;
            normal = transform.TransformDirection(normal);

            float dotProduct = Vector3.Dot(normal, Vector3.up);
            if (dotProduct > maxDotProduct)
            {
                maxDotProduct = dotProduct;
                faceIndex = face;

            }
        }

        //Debug.Log("Face Index: " + faceIndex);
        //Debug.Log("Largest Faces: " + string.Join(", ", largestFaces));

        if (faceIndexToNumber.TryGetValue(faceIndex, out int faceNumber))
        {
            //Debug.Log("Face Number: " + faceNumber);
            return faceNumber;
        }
        else
        {
            //Debug.Log("Face Index not found in dictionary");
            return -1; // or some other default value
        }
    }


    // Gets the largest faces of the mesh
    static List<int> GetLargestFaces(Mesh mesh, int count)
    {
        int[] triangles = mesh.triangles;
        Vector3[] vertices = mesh.vertices;
        List<(int index, float area)> faceAreas = new List<(int, float)>();

        for (int i = 0; i < triangles.Length; i += 3)
        {
            Vector3 v0 = vertices[triangles[i]];
            Vector3 v1 = vertices[triangles[i + 1]];
            Vector3 v2 = vertices[triangles[i + 2]];
            float area = Vector3.Cross(v1 - v0, v2 - v0).magnitude * 0.5f;
            faceAreas.Add((i / 3, area));
        }

        return faceAreas.OrderByDescending(f => f.area).Take(count).Select(f => f.index).ToList();
    }

    // Adds the text to the faces of the dice
    void AddTextToFaces(Mesh mesh, List<int> largestFaces)
    {
        Vector3[] vertices = mesh.vertices;
        int[] triangles = mesh.triangles;

        foreach (int i in largestFaces)
        {
            Vector3 v0 = vertices[triangles[i * 3]];
            Vector3 v1 = vertices[triangles[i * 3 + 1]];
            Vector3 v2 = vertices[triangles[i * 3 + 2]];
            Vector3 faceCenter = (v0 + v1 + v2) / 3;

            // Calcular a normal da face para orientar o texto
            Vector3 normal = Vector3.Cross(v1 - v0, v2 - v0).normalized;

            System.Array.Resize(ref Sides, Sides.Length + 1);

            Sides[^1] = new DiceSide
            {
                Center = faceCenter,
                Normal = normal,
                Value = faceIndexToNumber[i]
            };

            // Adicionar deslocamento na dire��o da normal
            Vector3 textPosition = faceCenter + normal * textOffset;

            GameObject textObj = Instantiate(textPrefab, transform);
            textObj.transform.localPosition = textPosition;

            // Calcular a dire��o da aresta selecionada para orientar o texto
            Vector3 selectedEdgeDirection = Vector3.zero;
            switch (egdes[faceIndexToNumber[i] - 1])
            {
                case 1:
                    selectedEdgeDirection = (v1 - v0).normalized;
                    break;
                case 2:
                    selectedEdgeDirection = (v2 - v1).normalized;
                    break;
                case 3:
                    selectedEdgeDirection = (v0 - v2).normalized;
                    break;
            }
            Quaternion rotation = Quaternion.LookRotation(normal, selectedEdgeDirection);

            // Adicionar 30 graus � rota��o em Z e inverter para alinhar a parte de baixo do texto
            rotation *= Quaternion.Euler(180, 0, 30);
            rotation *= Quaternion.Euler(0, 0, 180);

            textObj.transform.localRotation = rotation;

            textObj.name = faceIndexToNumber[i].ToString();

            textObj.GetComponent<TextMeshPro>().text = textObj.name;
            textObjects.Add(textObj);
        }

        Sides = Sides.OrderBy(s => s.Value).ToArray();
    }

    // Align the edge of the dice to the bottom
    void AlignEdgeToBottom(int faceNumber)
    {
        if (Time.timeScale < 0.9f) return; // make sure it dont interfere with Parry label
        Time.timeScale = 1f;

        // Encontra o indice da face e o objeto de texto
        int faceIndex = faceIndexToNumber.FirstOrDefault(x => x.Value == faceNumber).Key;
        if (faceIndex == -1) return;

        GameObject textObject = textObjects.FirstOrDefault(t => t.name == faceNumber.ToString());
        if (textObject == null) return;

        collisionCount = 0; // Makes the wall reapear when the dice is pulled up

        transform.SetPositionAndRotation(Vector3.Lerp(transform.position, startPos, Time.deltaTime * 10f), Quaternion.Lerp(transform.rotation, Quaternion.Euler(transform.rotation.eulerAngles.x, angles[faceNumber - 1], transform.rotation.eulerAngles.z), Time.deltaTime * 7f));

    }

    bool IsDiceMovementLow(Rigidbody diceRb, float threshold = 3f)
    {
        return diceRb.linearVelocity.magnitude < threshold && diceRb.angularVelocity.magnitude < threshold;
    }

}