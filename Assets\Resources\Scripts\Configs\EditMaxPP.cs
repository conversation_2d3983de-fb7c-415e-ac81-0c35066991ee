using TMPro;
using UnityEngine;

public class EditMaxPP : MonoBeh<PERSON>our
{
    private ConfigsHandler configsHandler; // Reference to the ConfigsHandler
    private TMP_InputField text;

    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script

        text = GetComponent<TMP_InputField>(); // Get the TMP_InputField component

        text.text = configsHandler.MaxPP.ToString(); // Add the listener to the TMP_InputField to edit the value

        text.onEndEdit.AddListener(EditValue); // Add the listener to the TMP_InputField to edit the value
    }

    void EditValue(string arg) // Edit the value of the energy timer
    {
        if (float.Parse(arg) <= 0) arg = "100"; // If the value is less than 0 set it to 100

        text.text = arg;

        configsHandler.MaxPP = float.Parse(arg); // Set the energy timer to the new value
        configsHandler.ppLeft = configsHandler.MaxPP; // Set the pp left to the max pp
        configsHandler.SaveValues(); // Save the value
    }
}

