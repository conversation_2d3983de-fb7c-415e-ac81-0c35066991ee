using System.Collections.Generic;
using System;
using System.IO;
using UnityEngine;
using System.Linq;
using System.Collections;
using UnityEngine.UI;
using TMPro;

public class LoadValues : MonoBehaviour
{
    public List<BattleCharacter> characters = new(); // list of characters

    public List<PartyCharacters> partyCharacters = new(); // list of party characters

    public List<CharacterSkills> defaultSkills = new(); // list of default skills

    public List<CharacterStatus> defaultStats = new(); // list of default stats

    public List<CharacterMods> defaultMods = new(); // list of default mods

    readonly int totalIfsOnLoad = 13;
    int totalIfsExecuted = 0, loadedChars = 0, totalChars;

    public Image loadingBar;
    public TextMeshProUGUI loadingInfo;

    public string loadingInfoText, dots = "";

    // default values
    public int energyTimer = 7, ModFraction = 10;

    public float B = 20, C = 2.5f, D = 0.5f, E = 0.04f, F = 1f, G = 1.5f, Difficulty = 1f, StockWeight = 0.5f, MaxPP = 100f, ReductionPerCombo = 1f, FractionOfAttacksPerAction = 2f, mainVolume = 1f, musicVolume = 1f, sfxVolume = 1f, IDBOffset = 0f;

    public bool musicEnabled = true, sfxEnabled = true;

    public int[] ComboChance = { 100, 100, 100, 100, 100, 100, 0 }, GoldenStrike = { 10, 20, 30, 40, 50, 60, 70 };

    public bool infinity = false, changeOnlyNulls = false, showHP = false, didLoad = false;

    private void Awake()
    {
        StartCoroutine(LoadSaveFiles());
        StartCoroutine(ProgressBarAnimation());
    }
    IEnumerator LoadingInfo()
    {
        dots = "";
        while (loadingInfo != null)
        {
            if (dots == "...") dots = "";
            dots += ".";

            yield return new WaitForSeconds(0.5f);
        }
    }

    IEnumerator ProgressBarAnimation()
    {
        StartCoroutine(LoadingInfo());

        float t = 0;
        while (loadingBar != null)
        {
            string extraInfo = loadingInfoText == "Characters" ? string.Format(" ({0} of {1})", loadedChars, totalChars) : "";

            loadingInfo.text = "Loading " + loadingInfoText + extraInfo + dots;

            if(!didLoad)
            {
                loadingBar.fillAmount = Mathf.Lerp(0, totalIfsExecuted / (float)totalIfsOnLoad, t);
                t += Time.deltaTime * 4;
            }
            yield return null;
        }
    }

    //
    // New JSON files (all use .json extension):
    // bonusChances.json, characters.json, damageVariables.json, etc.

    /// <summary>
    /// Loads data from JSON format, creates default if file doesn't exist
    /// </summary>
    private bool TryLoadFromJson<T>(string jsonFileName, out T data) where T : new()
    {
        data = default(T);

        // Try to load JSON file
        if (JsonSaveHelper.FileExists(jsonFileName))
        {
            try
            {
                data = JsonSaveHelper.LoadFromJson<T>(jsonFileName);
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[JSON] ❌ {jsonFileName} - LOAD FAILED: {ex.Message}");
            }
        }

        // Create default data and save to JSON
        data = new T();
        try
        {
            JsonSaveHelper.SaveToJson(data, jsonFileName);
        }
        catch (Exception ex)
        {
            Debug.LogError($"[JSON] ❌ {jsonFileName} - FAILED TO CREATE: {ex.Message}");
        }

        return false;
    }

    /// <summary>
    /// Creates default combo chances data
    /// </summary>
    private JsonComboChances CreateDefaultComboChances()
    {
        return new JsonComboChances { values = new int[] { 100, 100, 100, 100, 100, 100, 0 } }; // Default combo chances
    }

    /// <summary>
    /// Creates default skills list with one default skill set
    /// </summary>
    private JsonSkillsList CreateDefaultSkillsList()
    {
        var skillsList = new JsonSkillsList();
        skillsList.skills.Add(new JsonCharacterSkills(new CharacterSkills())); // Random skills
        return skillsList;
    }

    /// <summary>
    /// Creates default stats list with one default stat set
    /// </summary>
    private JsonStatusList CreateDefaultStatusList()
    {
        var statusList = new JsonStatusList();
        statusList.stats.Add(new JsonCharacterStatus(new CharacterStatus())); // Random stats
        return statusList;
    }

    /// <summary>
    /// Creates default mods list with one default mod set
    /// </summary>
    private JsonModsList CreateDefaultModsList()
    {
        var modsList = new JsonModsList();
        modsList.mods.Add(new JsonCharacterMods(new CharacterMods())); // Random mods
        return modsList;
    }

    /// <summary>
    /// Creates default characters list (empty)
    /// </summary>
    private JsonCharactersList CreateDefaultCharactersList()
    {
        return new JsonCharactersList(); // Empty character list
    }

    /// <summary>
    /// Creates default damage variables
    /// </summary>
    private JsonDamageVariables CreateDefaultDamageVariables()
    {
        return new JsonDamageVariables
        {
            B = 1.5f, C = 1.5f, D = 1.5f, E = 1.5f, F = 1.5f, G = 1.5f,
            Difficulty = 1f, StockWeight = 1f, MaxPP = 100,
            ReductionPerCombo = 0.1f, FractionOfAttacksPerAction = 0.5f,
            ModFraction = 10, IDBOffset = 0
        };
    }

    /// <summary>
    /// Creates default golden strike data
    /// </summary>
    private JsonGoldenStrike CreateDefaultGoldenStrike()
    {
        return new JsonGoldenStrike { values = new int[] { 10, 20, 30, 40, 50, 60, 70 } }; // Default golden strike values
    }

    /// <summary>
    /// Creates default energy amount
    /// </summary>
    private JsonEnergyAmount CreateDefaultEnergyAmount()
    {
        return new JsonEnergyAmount { energyTimer = 7 }; // Default energy timer
    }

    /// <summary>
    /// Creates default volume settings
    /// </summary>
    private JsonVolumeSettings CreateDefaultVolumeSettings()
    {
        return new JsonVolumeSettings
        {
            mainVolume = 1f, musicVolume = 1f, sfxVolume = 1f,
            musicEnabled = true, sfxEnabled = true
        };
    }

    /// <summary>
    /// Creates default party list with 5 empty parties
    /// </summary>
    private JsonPartiesList CreateDefaultPartiesList()
    {
        var partiesList = new JsonPartiesList();
        for (int i = 0; i < 5; i++)
        {
            partiesList.parties.Add(new JsonPartyCharacters($"Party{i}"));
        }
        return partiesList;
    }

    /// <summary>
    /// Creates default general info with sample keyword data
    /// Provides initial keyword structure for localization system
    /// </summary>
    private JsonGeneralInfo CreateDefaultGeneralInfo()
    {
        // Create sample keywords for demonstration and initial setup
        var defaultKeywords = new JsonKeyword[]
        {
            new JsonKeyword("cw100", "GENERIC_ACCEPT", "Confirmar", new[] { "KWT4" }),
            new JsonKeyword("cw101", "GENERIC_CANCEL", "Cancelar", new[] { "KWT4" }),
            new JsonKeyword("cw102", "GENERIC_OK", "OK", new[] { "KWT4" }),
            new JsonKeyword("cw103", "GENERIC_YES", "Sim", new[] { "KWT4" }),
            new JsonKeyword("cw104", "GENERIC_NO", "Não", new[] { "KWT4" }),
            new JsonKeyword("cw105", "GENERIC_BACK", "Voltar", new[] { "KWT4" }),
            new JsonKeyword("cw106", "GENERIC_NEXT", "Próximo", new[] { "KWT4" }),
            new JsonKeyword("cw107", "GENERIC_PREVIOUS", "Anterior", new[] { "KWT4" }),
            new JsonKeyword("cw108", "GENERIC_SAVE", "Salvar", new[] { "KWT4" }),
            new JsonKeyword("cw109", "GENERIC_LOAD", "Carregar", new[] { "KWT4" }),
            new JsonKeyword("cw110", "GENERIC_DELETE", "Excluir", new[] { "KWT4" }),
            new JsonKeyword("cw111", "GENERIC_EDIT", "Editar", new[] { "KWT4" }),
            new JsonKeyword("cw112", "GENERIC_NEW", "Novo", new[] { "KWT4" }),
            new JsonKeyword("cw113", "GENERIC_CLOSE", "Fechar", new[] { "KWT4" }),
            new JsonKeyword("cw114", "GENERIC_EXIT", "Sair", new[] { "KWT4" }),
            new JsonKeyword("cw115", "MENU_MAIN", "Menu Principal", new[] { "KWT1" }),
            new JsonKeyword("cw116", "MENU_SETTINGS", "Configurações", new[] { "KWT1" }),
            new JsonKeyword("cw117", "MENU_CHARACTERS", "Personagens", new[] { "KWT1" }),
            new JsonKeyword("cw118", "MENU_BATTLE", "Batalha", new[] { "KWT1" }),
            new JsonKeyword("cw119", "MENU_INVENTORY", "Inventário", new[] { "KWT1" })
        };

        return new JsonGeneralInfo(defaultKeywords);
    }

    /// <summary>
    /// Loads data from JSON format with custom default creation
    /// </summary>
    private bool TryLoadFromJsonWithDefaults<T>(string jsonFileName, out T data, Func<T> defaultCreator) where T : new()
    {
        data = default(T);

        // Try to load JSON file
        if (JsonSaveHelper.FileExists(jsonFileName))
        {
            try
            {
                data = JsonSaveHelper.LoadFromJson<T>(jsonFileName);
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[JSON] ❌ {jsonFileName} - LOAD FAILED: {ex.Message}");
            }
        }

        // Create custom default data and save to JSON
        data = defaultCreator();
        try
        {
            JsonSaveHelper.SaveToJson(data, jsonFileName);
        }
        catch (Exception ex)
        {
            Debug.LogError($"[JSON] ❌ {jsonFileName} - FAILED TO CREATE: {ex.Message}");
        }

        return false;
    }

    IEnumerator LoadSaveFiles()
    {
        var saveFolder = JsonSaveHelper.GetSaveFolder();

        if (Directory.Exists(saveFolder)) // checks if the save folder exists
        {
            totalIfsExecuted++;

            yield return new WaitForSeconds(0);
            loadingInfoText = "Bonus Combos";

            // Load bonus chances from JSON
            if (TryLoadFromJsonWithDefaults("bonusChances.json", out JsonComboChances comboData, CreateDefaultComboChances))
            {
                ComboChance = comboData.values ?? new int[] { 100, 100, 100, 100, 100, 100, 0 };
            }

            totalIfsExecuted++;

            yield return null;
            loadingInfoText = "Default Skills";

            // Load default skills from JSON
            if (TryLoadFromJsonWithDefaults("defaultSkills.json", out JsonSkillsList skillsWrapper, CreateDefaultSkillsList))
            {
                defaultSkills = JsonSaveHelper.ConvertSkillsFromJson(skillsWrapper.skills);
            }

            // Ensure we have at least one default skill
            if (defaultSkills.Count == 0)
            {
                defaultSkills.Add(new CharacterSkills());
            }

            totalIfsExecuted++;

            yield return new WaitForSeconds(0);
            loadingInfoText = "Default Status";

            // Load default stats from JSON
            if (TryLoadFromJsonWithDefaults("defaultStats.json", out JsonStatusList statsWrapper, CreateDefaultStatusList))
            {
                defaultStats = JsonSaveHelper.ConvertStatsFromJson(statsWrapper.stats);
            }

            // Ensure we have at least one default stat
            if (defaultStats.Count == 0)
            {
                defaultStats.Add(new CharacterStatus());
            }

            totalIfsExecuted++;

            yield return new WaitForSeconds(0);
            loadingInfoText = "Default Mods";

            // Load default mods from JSON
            if (TryLoadFromJsonWithDefaults("defaultMods.json", out JsonModsList modsWrapper, CreateDefaultModsList))
            {
                defaultMods = JsonSaveHelper.ConvertModsFromJson(modsWrapper.mods);
            }

            // Ensure we have at least one default mod
            if (defaultMods.Count == 0)
            {
                defaultMods.Add(new CharacterMods());
            }

            totalIfsExecuted++;

            yield return null;
            loadingInfoText = "Characters";

            // Migrate to chunked storage if needed
            JsonSaveHelper.MigrateToChunkedStorage();

            // Load characters from chunked storage or fallback to monolithic
            if (JsonSaveHelper.IsUsingChunkedStorage())
            {
                //Debug.Log("[LOAD] 📂 Loading characters from chunked storage...");
                characters = JsonSaveHelper.LoadAllCharactersChunked();
                totalChars = characters.Count;
                loadedChars = characters.Count;

                // Update character change tracker mapping
                CharacterChangeTracker.Instance.UpdateCharacterMapping(characters);

                //Debug.Log($"[LOAD] ✅ Loaded {characters.Count} characters from chunked storage");
            }
            else if (TryLoadFromJsonWithDefaults("characters.json", out JsonCharactersList charactersWrapper, CreateDefaultCharactersList))
            {
                //Debug.Log("[LOAD] 📄 Loading characters from monolithic file...");
                characters = JsonSaveHelper.ConvertCharactersFromJson(charactersWrapper.characters);
                totalChars = characters.Count;
                loadedChars = characters.Count;

                // Update character change tracker mapping
                CharacterChangeTracker.Instance.UpdateCharacterMapping(characters);

                //Debug.Log($"[LOAD] ✅ Loaded {characters.Count} characters from monolithic file");
            }

            totalIfsExecuted++;

            yield return new WaitForSeconds(0);
            loadingInfoText = "Damage Variables";

            // Load damage variables from JSON
            if (TryLoadFromJsonWithDefaults("damageVariables.json", out JsonDamageVariables damageData, CreateDefaultDamageVariables))
            {
                B = damageData.B; C = damageData.C; D = damageData.D; E = damageData.E; F = damageData.F; G = damageData.G;
                Difficulty = damageData.Difficulty; StockWeight = damageData.StockWeight; MaxPP = damageData.MaxPP;
                ReductionPerCombo = damageData.ReductionPerCombo; FractionOfAttacksPerAction = damageData.FractionOfAttacksPerAction;
                ModFraction = damageData.ModFraction; IDBOffset = damageData.IDBOffset; infinity = damageData.infinity;
                changeOnlyNulls = damageData.changeOnlyNulls; showHP = damageData.showHP;
            }

            totalIfsExecuted++;

            yield return new WaitForSeconds(0);
            loadingInfoText = "Golden Strikes";

            // Load golden strike from JSON
            if (TryLoadFromJsonWithDefaults("goldenStrike.json", out JsonGoldenStrike goldenData, CreateDefaultGoldenStrike))
            {
                GoldenStrike = goldenData.values ?? new int[] { 10, 20, 30, 40, 50, 60, 70 };
            }

            totalIfsExecuted++;

            loadingInfoText = "Energy Amount";

            // Load energy amount from JSON
            if (TryLoadFromJsonWithDefaults("energy.json", out JsonEnergyAmount energyData, CreateDefaultEnergyAmount))
            {
                energyTimer = energyData.energyTimer;
            }

            totalIfsExecuted++;

            yield return new WaitForSeconds(0);
            loadingInfoText = "Volume Settings";

            // Load volume settings from JSON
            if (TryLoadFromJsonWithDefaults("volumeSettings.json", out JsonVolumeSettings volumeData, CreateDefaultVolumeSettings))
            {
                mainVolume = volumeData.mainVolume;
                musicVolume = volumeData.musicVolume;
                sfxVolume = volumeData.sfxVolume;
                musicEnabled = volumeData.musicEnabled;
                sfxEnabled = volumeData.sfxEnabled;
            }

            totalIfsExecuted++;

            yield return new WaitForSeconds(0);
            loadingInfoText = "Parties";

            // Load parties from JSON
            if (TryLoadFromJsonWithDefaults("partyCharacters.json", out JsonPartiesList partiesWrapper, CreateDefaultPartiesList))
            {
                partyCharacters = JsonSaveHelper.ConvertPartiesFromJson(partiesWrapper.parties, characters);
            }
            else
            {
                for (int i = 0; i < 5; i++) partyCharacters.Add(new PartyCharacters("Party" + i));
            }

            totalIfsExecuted++;

            yield return new WaitForSeconds(0);
            loadingInfoText = "General Info";

            // Load general info (keywords) from JSON
            if (TryLoadFromJsonWithDefaults("generalInfo.json", out JsonGeneralInfo generalData, CreateDefaultGeneralInfo))
            {
                // Keywords are loaded by KeywordManager automatically when it initializes
                // This just ensures the file exists with proper structure
                Debug.Log($"[LOAD] ✅ General info loaded with {generalData.keywordPkg?.Length ?? 0} keywords");
            }

            totalIfsExecuted++;
        }

        totalIfsExecuted = totalIfsOnLoad;

        loadingInfoText = "Completed";
        while (loadingBar.fillAmount < 1f) yield return null;

        didLoad = true;

        GameObject.Find("FPS").SetActive(false);
        GameObject.Find("LoadingIcon").SetActive(false);

        DontDestroyOnLoad(gameObject); // keeps the gameobject alive across scenes
        new RestartGame(); // restarts the game to load the main scene
    }

    public void SaveParties(List<PartyCharacters> partyCharacters)
    {
        this.partyCharacters = partyCharacters;
        // Save party characters to JSON
        List<JsonPartyCharacters> jsonParties = JsonSaveHelper.ConvertPartiesToJson(partyCharacters);
        JsonPartiesList partiesWrapper = new JsonPartiesList(jsonParties);
        JsonSaveHelper.SaveToJson(partiesWrapper, "partyCharacters.json");
    }

    public void SaveCharacters(List<BattleCharacter> characters)
    {
        this.characters = characters;

        // Save characters using chunked storage system
        if (JsonSaveHelper.IsUsingChunkedStorage())
        {
            //Debug.Log("[SAVE] 💾 Saving characters using chunked storage (incremental)...");
            CharacterChangeTracker.Instance.SaveDirtyCharacters(characters);
        }
        else
        {
            //Debug.Log("[SAVE] 💾 Saving characters to monolithic file...");
            List<JsonBattleCharacter> jsonCharacters = JsonSaveHelper.ConvertCharactersToJson(characters);
            JsonCharactersList charactersWrapper = new JsonCharactersList(jsonCharacters);
            JsonSaveHelper.SaveToJson(charactersWrapper, "characters.json");
        }
    }

    public void SaveValues(float B, float C, float D, float E, float F, float G, float Difficulty, float StockWeight, float MaxPP, float ReductionPerCombo, float FractionOfAttacksPerAction, int ModFraction, float IDBOffset, bool infinity, bool showHP, bool changeOnlyNulls)
    {
        this.B = B;
        this.C = C;
        this.D = D;
        this.E = E;
        this.F = F;
        this.G = G;
        this.Difficulty = Difficulty;
        this.StockWeight = StockWeight;
        this.MaxPP = MaxPP;
        this.ReductionPerCombo = ReductionPerCombo;
        this.FractionOfAttacksPerAction = FractionOfAttacksPerAction;
        this.ModFraction = ModFraction;
        this.IDBOffset = IDBOffset;
        this.infinity = infinity;
        this.showHP = showHP;
        this.changeOnlyNulls = changeOnlyNulls;

        // Save damage variables to JSON
        JsonDamageVariables damageVars = new JsonDamageVariables
        {
            B = this.B, C = this.C, D = this.D, E = this.E, F = this.F, G = this.G,
            Difficulty = this.Difficulty, StockWeight = this.StockWeight, MaxPP = this.MaxPP,
            ReductionPerCombo = this.ReductionPerCombo, FractionOfAttacksPerAction = this.FractionOfAttacksPerAction,
            ModFraction = this.ModFraction, IDBOffset = this.IDBOffset, infinity = this.infinity,
            showHP = this.showHP, changeOnlyNulls = this.changeOnlyNulls
        };
        JsonSaveHelper.SaveToJson(damageVars, "damageVariables.json");
    }


    //public void SaveInfinity(bool infinity) => this.infinity = infinity;

    //public void SaveShowHP(bool showHP) => this.showHP = showHP;

    //public void SaveChangeOnlyNulls(bool changeOnlyNulls) => this.changeOnlyNulls = changeOnlyNulls;

    public void SaveComboChance(int[] ComboChance)
    {
        this.ComboChance = ComboChance;
        JsonComboChances comboData = new JsonComboChances { values = ComboChance };
        JsonSaveHelper.SaveToJson(comboData, "bonusChances.json");
    }

    public void SaveGoldenStrike(int[] GoldenStrike)
    {
        this.GoldenStrike = GoldenStrike;
        // Save golden strike to JSON
        JsonGoldenStrike goldenData = new JsonGoldenStrike { values = GoldenStrike };
        JsonSaveHelper.SaveToJson(goldenData, "goldenStrike.json");
    }

    public void SaveEnergyTimer(int energyTimer)
    {
        this.energyTimer = energyTimer;
        // Save energy timer to JSON
        JsonEnergyAmount energyData = new JsonEnergyAmount { energyTimer = this.energyTimer };
        JsonSaveHelper.SaveToJson(energyData, "energy.json");
    }

    public void SaveVolumeSettings(float mainVolume, float musicVolume, float sfxVolume)
    {
        this.mainVolume = mainVolume;
        this.musicVolume = musicVolume;
        this.sfxVolume = sfxVolume;
    }

    public void SaveVolumeSettings(float mainVolume, float musicVolume, float sfxVolume, bool musicEnabled, bool sfxEnabled)
    {
        this.mainVolume = mainVolume;
        this.musicVolume = musicVolume;
        this.sfxVolume = sfxVolume;
        this.musicEnabled = musicEnabled;
        this.sfxEnabled = sfxEnabled;
    }

    public void SaveVolumeToFile()
    {
        // Save volume settings to JSON format
        JsonVolumeSettings volumeSettings = new JsonVolumeSettings
        {
            mainVolume = this.mainVolume,
            musicVolume = this.musicVolume,
            sfxVolume = this.sfxVolume,
            musicEnabled = this.musicEnabled,
            sfxEnabled = this.sfxEnabled
        };

        JsonSaveHelper.SaveToJson(volumeSettings, "volumeSettings.json");
    }


    // General save method that saves all values
    public IEnumerator Save()
    {
        yield return null;

        // this is here in case that the values from here arent being used as a second reference of the characters in the configs handler
        ConfigsHandler configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();

        // Save characters using chunked storage system
        configsHandler.SaveCharacters();

        // Save damage variables to JSON
        configsHandler.SaveValues();

        // Save party characters to JSON
        configsHandler.SaveParties();

        // Save golden strike to JSON
        SaveGoldenStrike(GoldenStrike);

        // Save energy timer to JSON
        SaveEnergyTimer(energyTimer);

        // Save volume settings to JSON (already handled by SaveVolumeToFile method)
        SaveVolumeToFile();

        // Save party characters to JSON
        SaveParties(partyCharacters);

        // Update default lists with data from first 6 characters (if available)
        if (characters.Count > 0)
        {
            defaultSkills.Clear();
            defaultStats.Clear();
            defaultMods.Clear();

            int maxDefaults = Mathf.Min(characters.Count, 6);
            for (int i = 0; i < maxDefaults; i++)
            {
                defaultSkills.Add(characters[i].skills);
                defaultStats.Add(characters[i].stats);
                defaultMods.Add(characters[i].mods);
            }
        }

        // Save default skills to JSON
        List<JsonCharacterSkills> jsonDefaultSkills = JsonSaveHelper.ConvertSkillsToJson(defaultSkills);
        JsonSkillsList skillsWrapper = new JsonSkillsList(jsonDefaultSkills);
        JsonSaveHelper.SaveToJson(skillsWrapper, "defaultSkills.json");

        // Save default stats to JSON
        List<JsonCharacterStatus> jsonDefaultStats = JsonSaveHelper.ConvertStatsToJson(defaultStats);
        JsonStatusList statsWrapper = new JsonStatusList(jsonDefaultStats);
        JsonSaveHelper.SaveToJson(statsWrapper, "defaultStats.json");

        // Save default mods to JSON
        List<JsonCharacterMods> jsonDefaultMods = JsonSaveHelper.ConvertModsToJson(defaultMods);
        JsonModsList modsWrapper = new JsonModsList(jsonDefaultMods);
        JsonSaveHelper.SaveToJson(modsWrapper, "defaultMods.json");
    }
}