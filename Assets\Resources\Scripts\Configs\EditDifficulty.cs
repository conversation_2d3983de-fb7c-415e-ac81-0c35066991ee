using TMPro;
using UnityEngine;

public class EditDifficulty : MonoBeh<PERSON>our
{
    private ConfigsHandler configsHandler; // Reference to the ConfigsHandler
    private TMP_InputField text;

    private bool isEditting = false;

    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script

        text = GetComponent<TMP_InputField>(); // Get the TMP_InputField component

        text.onSelect.AddListener(delegate { isEditting = true; }); // Add the listener to the TMP_InputField to edit the value

        text.onEndEdit.AddListener(EditValue); // Add the listener to the TMP_InputField to edit the value
    }

    void Update()
    {
        if (!isEditting) text.text = configsHandler.Difficulty.ToString(); // Update the text with the current difficulty if is not editting
    }

    void EditValue(string arg) // Edit the value of the difficulty
    {
        if (float.Parse(arg) < 0) arg = "1"; // If the value is less than 0 set it to 1

        configsHandler.Difficulty = float.Parse(arg); // Set the difficulty to the new value

        isEditting = false;
        configsHandler.SaveValues(); // Save the value
    }
}
