using System;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class EnemyInterface : MonoBehaviour
{
    public GameObject ConfigHandlerOBJ; // Reference to the ConfigsHandler GameObject
    public GameObject Button; // Reference to the Button GameObject to show the values

    public Image AttackCooldown; // Reference to the Image to show the attack cooldown
    Image delayedHealthBar;

    public bool didCrit = false;

    public GameObject valueOBJ; // Reference to the EnemyValues GameObject

    public float pressTreshold = 0.25f;

    public BattleCharacter eC;

    EnemyAnimation enemyAnim;
    ConfigsHandler configsHandler; // Reference to the ConfigsHandler script
    Button button; // Reference to the Button component

    private readonly float animationDuration = 1.5f;
    private Tween delayTween;

    int enemyIndex; // Index of the enemy

    bool isPressing = false;
    bool longPressTriggered = false;
    float pressDuration = 0f;
    Coroutine longPressCoroutine = null;

    ScrollRect valueScroll;
    float scrollStopTime = 0f;
    readonly float scrollCooldown = 0.15f; // how long after scroll ends we ignore taps

    void Start()
    {
        enemyAnim = GetComponent<EnemyAnimation>();

        if (valueOBJ.activeSelf) valueOBJ.SetActive(false); // Set the EnemyValues GameObject to inactive

        enemyIndex = int.Parse(name[^1..]); // Get the index of the enemy

        configsHandler = ConfigHandlerOBJ.GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script
        button = Button.GetComponent<Button>(); // Get the Button component

        delayedHealthBar = Button.transform.GetChild(2).GetChild(0).GetComponent<Image>();

        AttackCooldown.fillAmount = 0; // Set the attack cooldown to 0

        button.onClick.AddListener(() => // Add the listener to the button to show the values
        {
            if (!valueOBJ.activeSelf)
            {

                // play the select sound effect
                ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/Select"));

                configsHandler.SetEnemySelected(enemyIndex);
            }
        });

        valueScroll = valueOBJ.GetComponentInChildren<ScrollRect>();
        valueScroll.onValueChanged.AddListener(_ => {
            scrollStopTime = Time.time + scrollCooldown;
        });

        // Set up long press detection
        EventTrigger trigger = Button.AddComponent<EventTrigger>();

        // Pointer Down
        EventTrigger.Entry pointerDown = new EventTrigger.Entry
        {
            eventID = EventTriggerType.PointerDown
        };
        pointerDown.callback.AddListener((_) =>
        {
            if (!valueOBJ.activeSelf)
            {
                isPressing = true;
                longPressTriggered = false;
                pressDuration = 0f;
                longPressCoroutine = StartCoroutine(CheckLongPress());
            }
        });
        trigger.triggers.Add(pointerDown);

        //Setting up pointers for showing values
        // Pointer Up
        EventTrigger.Entry pointerUp = new EventTrigger.Entry
        {
            eventID = EventTriggerType.PointerUp
        };
        pointerUp.callback.AddListener((_) =>
        {
            isPressing = false;
            pressDuration = 0f;
            if (longPressCoroutine != null)
            {
                StopCoroutine(longPressCoroutine);
                longPressCoroutine = null;
            }
        });
        trigger.triggers.Add(pointerUp);

    }

    IEnumerator ShowSkillValues()
    {
        yield return null;

        // Safety check for touch input
        if (Input.touchCount <= 0) yield break;

        try
        {
            Vector3 cursorPos = Camera.main.ScreenToWorldPoint(Input.GetTouch(0).position);

            valueOBJ.SetActive(true);
            float x = Mathf.Max(-0.7f, Mathf.Min(0.7f, cursorPos.x));
            float y = cursorPos.y + 1f > 4.3f ? cursorPos.y - 1f : cursorPos.y + 1f;

            valueOBJ.transform.position = new Vector3(x, y, transform.position.z) + new Vector3(0f, 0f, -1.25f);
            valueOBJ.GetComponent<EnemyValues>().UpdateCharacter(configsHandler.GetEnemyCharacter(enemyIndex));
        }
        catch (ArgumentException)
        {
            // Touch was lost during processing
            Debug.LogWarning("Touch input was lost during processing");
        }
    }

    void Update()
    {
        eC = configsHandler.GetEnemyCharacter(enemyIndex); // Get the enemy character

        if (eC != null && !eC.IsDead) // If the enemy is not dead it updates the values
        {
        if (Input.touchCount > 0 && Input.GetTouch(0).phase == TouchPhase.Ended)
        {
            // Ignore tap if recently scrolling, prevents accidental higing of character values
            if (Time.time < scrollStopTime)
            {
                return;
            }

            if (valueOBJ.activeSelf && !IsTouchOverGameObject(valueOBJ))
            {
                valueOBJ.SetActive(false);
            }
        }

            transform.GetChild(0).gameObject.SetActive(true); // Set the EnemyInterface GameObject to active

            // Reduce the attack cooldown of the enemy using its speed as a percentage,
            // if the speed is 100, it will take 1 second to get to 0
            // and the lower the speed the slower it will get to 0
            if (AttackCooldown.fillAmount > 0.0001f) AttackCooldown.fillAmount -= Mathf.Max(1, eC.mods.GetSpeed()) / 100f * Time.deltaTime;


            // Updates the health bar of the enemy, reducing it smoothly to the current health
            Button.transform
                .GetChild(2)
                .GetChild(1)
                .GetComponent<Image>().fillAmount = (eC.maxHP == 0) ? 1 :
                    Mathf.MoveTowards(Button.transform
                        .GetChild(2)
                        .GetChild(1)
                        .GetComponent<Image>().fillAmount, (float)eC.hP / eC.maxHP, Time.deltaTime);

            float targetFillAmount = (eC.maxHP == 0) ? 1 : (float)eC.hP / eC.maxHP;
            UpdateHealthBarDelay(targetFillAmount); // delay for the health bar

            Button.transform
                .GetChild(2)
                .GetChild(1)
                .GetComponent<Image>().color =
                Tools.InterpolateColor(
                    new(178f / 255f, 250f / 255f, 67f / 255f),
                    Color.red,
                    Button.transform
                        .GetChild(2)
                        .GetChild(1)
                        .GetComponent<Image>().fillAmount,
                    5f);

            // Updates the text of the health bar to show the current health and the max health,
            // this is still here if you want to show the health of the enemy, for that
            // use eC.hP.ToString() + "/" + eC.maxHP.ToString() instead of "???"
            GameObject healthbar =
            Button.transform
                .GetChild(2)
                .GetChild(2).gameObject;

            TextMeshProUGUI healthbartext = healthbar.transform
                .GetChild(0)
                .GetComponent<TextMeshProUGUI>();

            healthbartext.text = configsHandler.ShowHP.GetComponent<Toggle>().isOn ? eC.hP.ToString() + "/" + eC.maxHP.ToString() : "???";

            float size = healthbartext.GetPreferredValues().x + 0.19f;

            healthbar.GetComponent<RectTransform>().SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal, size);

            // Updates the label that contains the enemy's name
            Button.transform
                .GetChild(3)
                .GetComponent<TextMeshProUGUI>().text = eC.name;

            Button.transform
                .GetChild(5)
                .gameObject.SetActive(eC.isStunned);
        }
        else // else it removes the cooldown and hides it
        {
            if (AttackCooldown.fillAmount > 0.0001f) AttackCooldown.fillAmount = 0;
            Button.SetActive(false);
        }

        // shows the selected highlight if the enemy is selected
        if (configsHandler.GetNumberOfEnemies() > 1)
        {
            Button.transform.GetChild(1).gameObject.SetActive(configsHandler.GetEnemySelected(enemyIndex));
        }

    }

    void UpdateHealthBarDelay(float targetFillAmount)
    {
        // Kill existing tween to prevent overlapping
        delayTween?.Kill();

        // Start smooth animation with ease-in
        delayTween = delayedHealthBar.DOFillAmount(targetFillAmount, animationDuration)
            .SetEase(Ease.OutQuad);
    }

    bool IsPointerOverButton()
    {
        Vector2 cursorPos = Input.GetTouch(0).position;
        PointerEventData pointerData = new(EventSystem.current)
        {
            position = cursorPos
        };

        List<RaycastResult> raycastResults = new();
        EventSystem.current.RaycastAll(pointerData, raycastResults);

        foreach (RaycastResult result in raycastResults)
        {
            if (result.gameObject != Button)
            {
                return false; // Há outro objeto realizando raycasting
            }
        }

        return RectTransformUtility.RectangleContainsScreenPoint(Button.GetComponent<RectTransform>(), cursorPos, Camera.main);
    }

    public bool IsHPDepliding()
    {
        if (eC == null) return false;

        float healthBar = Button.transform
                        .GetChild(2)
                        .GetChild(1)
                        .GetComponent<Image>().fillAmount;

        float healthAmount = (float)eC.hP / eC.maxHP;

        return healthBar > healthAmount || enemyAnim.gotHitted;
    }

    bool IsTouchOverGameObject(GameObject obj)
    {
        PointerEventData pointerData = new PointerEventData(EventSystem.current)
        {
            position = Input.GetTouch(0).position
        };

        List<RaycastResult> results = new List<RaycastResult>();
        EventSystem.current.RaycastAll(pointerData, results);

        foreach (var result in results)
        {
            if (result.gameObject == obj || result.gameObject.transform.IsChildOf(obj.transform))
                return true;
        }
        return false;
    }

    IEnumerator CheckLongPress()
    {
        while (isPressing)
        {
            pressDuration += Time.deltaTime;
            if (pressDuration >= pressTreshold && !longPressTriggered)
            {
                longPressTriggered = true;
                StartCoroutine(ShowSkillValues());
                break;
            }
            yield return null;
        }
    }
}
