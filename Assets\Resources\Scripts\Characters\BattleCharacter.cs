using UnityEngine;
using System.Collections.Generic;
using System.Linq;

public class BattleCharacter
{
    public string id;
    public string name;
    public string classe;
    public string rarity;
    public long maxHP;
    public long hP;
    public bool isEnemy = false;
    public bool isGuard = false;

    public bool hpAddapted = false;

    public StunPhase stunPhase = StunPhase.Normal;

    public bool isStunned = false;
    public bool canReceiveCoins = false;
    public int combos = -1;

    public int baseLevel = 1;
    public int level = 1;

    public bool missCheck = false;

    public Dictionary<string, StatusIcon> statusIcons = new(); // <StatusName, StatusIcon>

    public CharacterStatus stats; // The stats of the character, contains the respective hp, atk and def for each level
    public CharacterSkills skills; // The skills of the character, contains the Spacial Attack and Special Defense of each type, the types are in the enum Types.cs
    public CharacterMods mods; // The mods of the character, contains the values of knowledge, luck, speed, precision, evasion and critical chance
    public CharacterAilementDefenses ailDefs; // The ailment defenses of the character, contains the values of charm, confusion, curse, paralysis and sleep

    public ConfigsHandler configsHandler = null;
    GridManager gridManager = null;

    // Buffs and Debuffs of Defense
    public List<int> defEquals = new();
    public List<float> defMultipliers = new();
    public List<int> defAdders = new();

    // Buffs and Debuffs of Attack
    public List<int> atkEquals = new();
    public List<float> atkMultipliers = new();
    public List<int> atkAdders = new();

    // Buffs and Debuffs of Special Defense
    public List<int> spDefEquals = new();
    public List<float> spDefMultipliers = new();
    public List<int> spDefAdders = new();

    // Buffs and Debuffs of Special Attack
    public List<int> spAtkEquals = new();
    public List<float> spAtkMultipliers = new();
    public List<int> spAtkAdders = new();

    public string activeTab;

    public int healingAmount = 0;
    bool phyAttack;

    public int stunCount;


    public BattleCharacter(string id, CharacterStatus stats = null, CharacterSkills skills = null, CharacterMods mods = null, CharacterAilementDefenses ailDefs = null)
    {
        name = "New" + id; // default name

        this.stats = new();
        this.skills = new();
        this.mods = new();
        this.ailDefs = new();


        if (stats != null && stats.bl.Count > 0) this.stats = stats;
        if (skills != null && skills.GetAllDef().Length > 0) this.skills = skills;
        if (mods != null) this.mods = mods;
        if (ailDefs != null) this.ailDefs = ailDefs;

        maxHP = this.stats.GetHp(level); // sets the maxHP at level 1

        hP = maxHP;
        this.id = id;
    }

    public bool IsDead => hP <= -1; // checks if the character is dead

    public void SetLevel(int level) // sets the level and resets the hp
    {
        this.level = level;
        maxHP = stats.GetHp(level);
        hP = maxHP;
    }

    public bool Damage(long dmg, int spAtk, Types type, BattleCharacter attacker, bool isCounter = false) // damages the character
    {
        bool didCrit = false;
        bool didWeak = false;
        bool didSuperEffective = false;
        bool didNotEffective = false;
        bool missed = false;
        bool didAbsorb = false;
        bool didRepel = false;
        bool didBlock = false;
        bool parried = false;
        float parryChance = 0; // Temporary value

        healingAmount = 0; // resets the healing amount

        if (configsHandler == null) configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // gets the configs handler if it is null
        if (gridManager == null) gridManager = GameObject.Find("GridManager").GetComponent<GridManager>();

        float B = configsHandler.B, C = configsHandler.C, D = configsHandler.D; // gets the values of B, C and D

        int _def = Mathf.Max(1, stats.GetDef(level)); // makes def at least 1

        if (defEquals.Count > 0) // set the Equals value for def
            _def = defEquals[^1];

        if (defAdders.Count > 0) // set the Adders values for def
            _def = (int)defAdders.Aggregate((float)_def, (acc, number) => acc + number);

        if (defMultipliers.Count > 0) // set the Multipliers values for def
            _def = (int)defMultipliers.Aggregate((float)_def, (acc, number) => acc * number);


        int spAtkProcessed = spAtk;

        if (spAtkEquals.Count > 0) // set the Equals value for spAtk
            spAtkProcessed = attacker.spAtkEquals[^1];

        if (attacker.spAtkAdders.Count > 0) // set the Adders values for spAtk
            spAtkProcessed = (int)attacker.spAtkAdders.Aggregate((float)spAtkProcessed, (acc, number) => acc + number);

        if (attacker.spAtkMultipliers.Count > 0) // set the Multipliers values for spAtk
            spAtkProcessed = (int)attacker.spAtkMultipliers.Aggregate((float)spAtkProcessed, (acc, number) => acc * number);

        float F = configsHandler.F; // gets the F

        float G = configsHandler.G; // gets the G

        float attackRange = 1 + 0.01f * Mathf.Min(spAtkProcessed, 100) + (F * Mathf.Pow(Mathf.Max(0, spAtkProcessed - 100), G));

        long dmgProcessed = dmg;

        if (atkEquals.Count > 0) // set the Equals value for atk
            dmgProcessed = attacker.atkEquals[^1];

        if (attacker.atkAdders.Count > 0) // set the Adders values for atk
            dmgProcessed = (int)attacker.atkAdders.Aggregate((float)dmgProcessed, (acc, number) => acc + number);

        if (attacker.atkMultipliers.Count > 0) // set the Multipliers values for atk
            dmgProcessed = (int)attacker.atkMultipliers.Aggregate((float)dmgProcessed, (acc, number) => acc * number);

        float critHit = 1f;


        float mod = mods.GetEvasion() / 100f * (attacker.mods.GetPrecision() / 100f) + (1f - (attacker.mods.GetPrecision() / 100f));

        if (mod / Mathf.Max(1, configsHandler.ModFraction) >= Random.value && !isStunned && !isCounter)
        {
            missed = true;
            missCheck = true;

            mod = 0f;
        }
        else { mod = 1f; missCheck = false; }



        if (Random.Range(0, 100) < attacker.mods.GetCriticalChance())
        {
            critHit = Mathf.Max(1.5f, 1 + attacker.mods.GetPrecision() * attacker.mods.GetLuck() / (100 * 12) * 2);
            didCrit = true;
        }


        if (skills.GetValues(type).spDef == "Absorver" && !string.IsNullOrEmpty(attacker.skills.GetValues(type).spAtk) && !attacker.skills.GetValues(type).spAtk.Equals("0")) didAbsorb = true;
        if (skills.GetValues(type).spDef == "Repelir" && !string.IsNullOrEmpty(attacker.skills.GetValues(type).spAtk) && !attacker.skills.GetValues(type).spAtk.Equals("0")) didRepel = true;
        if (skills.GetValues(type).spDef == "Nulo" && !string.IsNullOrEmpty(attacker.skills.GetValues(type).spAtk) && !attacker.skills.GetValues(type).spAtk.Equals("0")) didBlock = true;
        else if (int.TryParse(skills.GetValues(type).spDef, out int spDefValue))
        {
            if (Mathf.Min(skills.GetAllDef()) == spDefValue && spDefValue < 1) didSuperEffective = true;
            if (Mathf.Max(skills.GetAllDef()) == spDefValue) didNotEffective = true;
            if (spDefValue < 0 && Mathf.Min(skills.GetAllDef()) != spDefValue) didWeak = true;
        }

        if (!isEnemy && combos >= -1 && !missed) combos++;  //Count combo from enemy on player. Deals with stun logic for player only

        float stunDmg = 1f;

        if ((combos >= 5 || isStunned) && configsHandler.parryCount < 1 ) // Player can only get stunned if didnt parry (same for enemy)
        {
            if (!isEnemy)  // Only make player stunned, enemy stun logic is handled in GridManager.cs
            {
                isStunned = true;
                stunCount++;
            }
        }

        if (isStunned) stunDmg = 1.5f; // Stunned chars receive 50% more damage

        if (isCounter) _def /= 2; // Counters ignore 50% of base DEF

        long dmgTaken = (long)Mathf.Round(critHit * B * (dmgProcessed * attackRange / Mathf.Max(_def, 1)) * Mathf.Pow(C / ReturnResistanceRange(type, attacker), D) * mod * stunDmg); // calculates the damage

        if (!isCounter && !missed && phyAttack && !isStunned && Random.Range(0, 100) < parryChance) // If parries dmg taken is 0
        {
            didAbsorb = false;
            didBlock = false;
            didRepel = false;
            parried = true;
            dmgTaken = 0;
        }

        if (didBlock && !isStunned && !isCounter) dmgTaken = 0; // if the character blocked the attack, the damage is 0

        if (dmgTaken < 0) dmgTaken = 0; // makes sure the damage is not negative

        if (isGuard) // reduces the damage by 40% if the character is guarding, removes the status icon and breaks the guard
        {
            dmgTaken = (int)(dmgTaken * 0.4f);
            configsHandler.playersInterface[configsHandler.GetPlayerIndex(this)].GetComponent<PlayerInterface>().RemoveStatusIcon("Guarding");
            isGuard = false;
        }
        if (didAbsorb && !isStunned && !isCounter)  // heals the character if the attack was absorbed
        {
            Heal(dmgTaken); healingAmount = (int)dmgTaken;
            HealLabel.Instance.ShowHeal(dmgTaken, this);
        }
        else if (didRepel && !isStunned && !isCounter) attacker.hP -= dmgTaken; // repels the damage to the attacker
        else hP -= dmgTaken; // takes the damage

        if (isStunned) // If stunned there is no absorb repel or block label
        {
            didAbsorb = false;
            didRepel = false;
            didBlock = false;
        }

        if (isCounter) // Counter should only be counter
        {
            didCrit = false;
            didWeak = false;
            didSuperEffective = false;
            didNotEffective = false;
            missed = false;
            didAbsorb = false;
            didRepel = false;
            didBlock = false;
            parried = false;
        }

        configsHandler.DamageLabel(this, dmgTaken, type, didCrit, didWeak, didSuperEffective, didNotEffective, missed, didAbsorb, didRepel, didBlock, parried, isCounter); // shows the damage

        if (hP <= 0) hP = -1; // sets the hp to -1 if the character is dead, this is like this because the enemies hp can be 0 with theres no player on the player interface

        return missed;
    }

    public float ReturnResistanceRange(Types type, BattleCharacter attacker) // returns the resistance range of a type
    {
        float _spDef = 0f;
        string SpAtkRaw;

        if (configsHandler.isCounter) SpAtkRaw = "0"; else SpAtkRaw = attacker.skills.GetValues(type).spAtk; // If is a counter makes attack makes spDef = 0 since spDef will be 0
        string SpAtkNormalized = string.IsNullOrWhiteSpace(SpAtkRaw) ? "0" : SpAtkRaw;

        if (int.TryParse(SpAtkNormalized, out int froSpAtk2) && froSpAtk2 <= 0) // If the attack (spAtk = 0)  is physical the defense should also be physical (spDef = 0)
        {
            phyAttack = true;
            _spDef = 0;
        }
        else
        {
            phyAttack = false;

            if (int.TryParse(skills.GetValues(type).spDef, out int spDefValue))
                _spDef = spDefValue; // gets the spDef of the type

            if (spDefEquals.Count > 0) // set the Equals value for spDef
                _spDef = spDefEquals[^1];

            if (spDefAdders.Count > 0) // set the Adders values for spDef
                _spDef = spDefAdders.Aggregate(_spDef, (acc, number) => acc + number);

            if (spDefMultipliers.Count > 0) // set the Multipliers values for spDef
                _spDef = spDefMultipliers.Aggregate(_spDef, (acc, number) => acc * number);
        }

        float E = configsHandler.E; // gets the E

        return 0.01f * Mathf.Max(_spDef, 0) + Mathf.Exp(E * Mathf.Min(_spDef, 0));
    }

    public void Heal(long amount) // heals the character, not exactly implemented, for now im using to heal the enemy if their hp is incorrect for the amount of players
    {
        if (hP + amount < maxHP) hP += amount;
        else hP = maxHP;
    }

    public void ResetBuffs() // resets the buffs
    {
        isGuard = false;
        isStunned = false;

        combos = -1;

        statusIcons.Clear();

        atkEquals.Clear();
        atkAdders.Clear();
        atkMultipliers.Clear();

        defEquals.Clear();
        defAdders.Clear();
        defMultipliers.Clear();

        spAtkEquals.Clear();
        spAtkAdders.Clear();
        spAtkMultipliers.Clear();

        spDefEquals.Clear();
        spDefAdders.Clear();
        spDefMultipliers.Clear();
    }
}
