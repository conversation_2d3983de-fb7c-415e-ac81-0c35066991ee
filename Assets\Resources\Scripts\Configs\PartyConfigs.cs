using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;
using System.Linq;

/// <summary>
/// Class that handles the party configs
/// </summary>
public class PartyConfigs : MonoBehaviour
{
    // ConfigsHandler reference
    ConfigsHandler configsHandler;
    public StickyOverlayForStock stickyOverlayForStock;
    public StickyUIElement stickyUIElement;

    // ToggleGroups of the party being used and the party that is being eddited
    ToggleGroup useParty, editParty;

    // List of the gameobjects of the parties
    readonly List<GameObject> partiesOBJ = new();

    // Bool that checks if the game has started
    bool didGameStart = false;

    // Float that controls the spacing between the characters
    readonly float spacing = 6f;

    // Int that controls the index of the party
    public int partyIndex = 0;

    // Sprite cache for Types
    private readonly Dictionary<Types, Sprite> typeSpriteCache = new();

    void Start()
    {
        // Get the ConfigsHandler script
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();

        // Get the ToggleGroup of the party being used
        useParty = GetComponent<ToggleGroup>();

        // Get the ToggleGroup of the party being eddited
        editParty = transform.parent.parent.GetChild(0).GetComponent<ToggleGroup>();

        // Gets the gameObject of each party
        for (int i = 0; i < 5; i++) partiesOBJ.Add(transform.GetChild(i).gameObject);

        // Adds the onEndEdit listener to the TMP_InputField
        foreach (GameObject party in partiesOBJ)
        {
            TMP_InputField partyName = party.transform.GetChild(0).GetComponent<TMP_InputField>();

            partyName.onEndEdit.AddListener(text => PartyNameEditted(text, partyName));

            SetupButtonListeners(party);
        }

        LoadAllTypeSprites();

        RefreshPartyUI();

        // Update the UI to each party
        Toggle[] editPartyToggles = editParty.GetComponentsInChildren<Toggle>();
        foreach (var toggle in editPartyToggles)
        {
            toggle.onValueChanged.AddListener((isOn) =>
            {
                if (isOn)
                {
                    // Delay slightly to ensure the ToggleGroup has updated
                    StartCoroutine(RefreshNextFrame());
                }
            });
        }
    }

    IEnumerator RefreshNextFrame() // Without this you would have to click the toggle twice
    {
        yield return null; // wait one frame
        RefreshPartyUI();
        stickyUIElement.SwitchStickyOriginal(partyIndex);
    }

    void SetupButtonListeners(GameObject party)
    {
        var allButtons = party.GetComponentsInChildren<Button>();
        foreach (var slot in allButtons)
        {

            // Clear existing listeners to avoid duplicates
            slot.onClick.RemoveAllListeners();

            if (slot.name != "remChar" && slot.name != "AstralPlanBtn") // if the button's name isn't "remChar" and "AstralPlanBtn" it gives the listener for it for selecting the slots of the party
            {
                slot.onClick.AddListener(delegate { StartCoroutine(SlotSelected(slot)); });
                var sbImage = slot.GetComponentsInChildren<Image>().FirstOrDefault(it => it.name == "SB");
                if (sbImage != null)
                {
                    sbImage.enabled = false;
                }
            }
        }
    }

    void Update()
    {
        if (configsHandler.turns != 0) didGameStart = true; // checks if the game has started

        // if the game has started, disables the toggles to select the party
        if (didGameStart) foreach (var useToggle in useParty.gameObject.GetComponentsInChildren<Toggle>()) useToggle.interactable = false;

        // Ensure button listeners are set up (in case buttons are created dynamically)
        foreach (GameObject party in partiesOBJ)
        {
            party.transform.GetChild(4).GetChild(1).GetComponent<TextMeshProUGUI>().text = configsHandler.CalculateAverageActivePartySpeed().ToString() == "NaN" ? "Select party" : configsHandler.CalculateAverageActivePartySpeed().ToString();
        }

        // gets the toggle of the party being used
        Toggle usePartyToggle = useParty.ActiveToggles().FirstOrDefault();

        // sets the selected party
        configsHandler.SetSelectedParty(usePartyToggle != null ? usePartyToggle.transform.parent.GetSiblingIndex() : -1);

        // gets the index of the party
        partyIndex = editParty.ActiveToggles().FirstOrDefault().transform.GetSiblingIndex();
    }


    public void RefreshPartyUI(int onlyPartyIndex = -1)
    {
        foreach (GameObject party in partiesOBJ) // updates the values being displayed
        {

            if (onlyPartyIndex != -1 && party.transform.GetSiblingIndex() != onlyPartyIndex)
                continue;
            // gets the TMP_InputField for the name of the party and the PartyCharacters
            TMP_InputField partyName = party.transform.GetChild(0).GetComponent<TMP_InputField>();
            PartyCharacters partyCharacters = configsHandler.GetPartyCharacters(party.transform.GetSiblingIndex());

            // if the PartyCharacters is null, breaks
            if (partyCharacters == null) break;

            // if the TMP_InputField isn't focused, updates the name
            if (!partyName.isFocused) partyName.text = partyCharacters.name;

            // updates the active characters being displayed
            foreach (Button activeButtons in party.transform.GetChild(1).GetChild(1).GetComponentsInChildren<Button>().Where(b => b.name != "remChar"))
            {
                // gets the character
                BattleCharacter character = partyCharacters.activeCharacters[activeButtons.transform.GetSiblingIndex()];
                if (character != null) // if the character isn't null, it show the values
                {
                    activeButtons.transform.GetChild(0).gameObject.SetActive(false);
                    activeButtons.transform.GetChild(1).gameObject.SetActive(true);
                    activeButtons.transform.GetChild(2).gameObject.SetActive(true);
                    activeButtons.transform.GetChild(3).gameObject.SetActive(true);
                    activeButtons.transform.GetChild(4).gameObject.SetActive(true);
                    activeButtons.transform.GetChild(6).gameObject.SetActive(true);
                    activeButtons.transform.GetChild(7).gameObject.SetActive(true);
                    activeButtons.transform.GetChild(8).gameObject.SetActive(true);
                    activeButtons.transform.GetChild(11).gameObject.SetActive(true);
                    var remCharButtonGO = activeButtons.transform.GetChild(11).gameObject;
                    remCharButtonGO.SetActive(true);

                    Button remCharButton = remCharButtonGO.GetComponent<Button>();
                    if (remCharButton != null)
                    {
                        remCharButton.onClick.RemoveAllListeners();  // clear old listeners
                        remCharButton.onClick.AddListener(delegate { RemoveCharacterFromParty(remCharButton); });
                    }

                    activeButtons.transform.GetChild(2).GetComponent<TextMeshProUGUI>().text = character.name;
                    activeButtons.transform.GetChild(3).GetComponent<TextMeshProUGUI>().text = "Lv: " + character.level.ToString();
                    activeButtons.transform.GetChild(4).GetChild(0).GetComponent<Image>().fillAmount = (character.maxHP == 0) ? 1 : (float)character.hP / character.maxHP;
                    activeButtons.transform.GetChild(4).GetChild(1).GetChild(0).GetComponent<TextMeshProUGUI>().text = character.hP.ToString();
                    Types type1 = character.skills.GetHighestSpDef();
                    activeButtons.transform.GetChild(6).GetChild(0).GetComponent<Image>().sprite = GetTypeSprite(type1);
                    Types type2 = character.skills.GetHighestSpAtk();
                    activeButtons.transform.GetChild(7).GetChild(0).GetComponent<Image>().sprite = GetTypeSprite(type2);
                    Types type3 = character.skills.GetLowestSpDef();
                    activeButtons.transform.GetChild(8).GetChild(0).GetComponent<Image>().sprite = GetTypeSprite(type3);

                    if (character.IsDead)
                    {
                        activeButtons.transform.GetChild(9).gameObject.SetActive(true);
                        activeButtons.transform.GetChild(10).gameObject.SetActive(true);
                        activeButtons.transform.GetChild(9).GetComponent<Image>().color = new Color(1f, 0f, 0f, 0.4f);
                        activeButtons.transform.GetChild(10).GetComponent<TextMeshProUGUI>().text = "DEAD";

                    }
                    else
                    {
                        activeButtons.transform.GetChild(9).gameObject.SetActive(false);
                        activeButtons.transform.GetChild(10).gameObject.SetActive(false);
                    }

                }
                else // otherwise it hide the values
                {
                    activeButtons.transform.GetChild(0).gameObject.SetActive(true);
                    activeButtons.transform.GetChild(1).gameObject.SetActive(false);
                    activeButtons.transform.GetChild(2).gameObject.SetActive(false);
                    activeButtons.transform.GetChild(3).gameObject.SetActive(false);
                    activeButtons.transform.GetChild(4).gameObject.SetActive(false);
                    activeButtons.transform.GetChild(6).gameObject.SetActive(false);
                    activeButtons.transform.GetChild(7).gameObject.SetActive(false);
                    activeButtons.transform.GetChild(8).gameObject.SetActive(false);
                    activeButtons.transform.GetChild(9).gameObject.SetActive(false);
                    activeButtons.transform.GetChild(10).gameObject.SetActive(false);
                    activeButtons.transform.GetChild(11).gameObject.SetActive(false);
                }
            }

            // updates the stock characters being displayed
            foreach (Button stockButtons in party.transform.GetChild(2).GetChild(1).GetComponentsInChildren<Button>().Where(b => b.name != "remChar"))
            {
                // gets the character
                BattleCharacter character = partyCharacters.stockCharacters[stockButtons.transform.GetSiblingIndex()];

                if (character != null) // if the character isn't null, it show the values
                {
                    bool isCharNotActive = partyCharacters.activeCharacters.FirstOrDefault(c => c == character) == null;
                    bool isCharDead = character.IsDead;

                    stockButtons.interactable = isCharNotActive;

                    stockButtons.transform.GetChild(0).gameObject.SetActive(false);
                    stockButtons.transform.GetChild(1).gameObject.SetActive(true);
                    stockButtons.transform.GetChild(2).gameObject.SetActive(true);
                    stockButtons.transform.GetChild(3).gameObject.SetActive(true);
                    stockButtons.transform.GetChild(5).gameObject.SetActive(true);
                    stockButtons.transform.GetChild(6).gameObject.SetActive(true);
                    stockButtons.transform.GetChild(7).gameObject.SetActive(true);
                    stockButtons.transform.GetChild(8).gameObject.SetActive(true);
                    stockButtons.transform.GetChild(9).gameObject.SetActive(!isCharNotActive);
                    stockButtons.transform.GetChild(10).gameObject.SetActive(!isCharNotActive);
                    stockButtons.transform.GetChild(11).gameObject.SetActive(isCharNotActive);
                    var remCharButtonGO = stockButtons.transform.GetChild(11).gameObject;
                    remCharButtonGO.SetActive(isCharNotActive);

                    if (isCharNotActive) // only add listener if active
                    {
                        Button remCharButton = remCharButtonGO.GetComponent<Button>();
                        if (remCharButton != null)
                        {
                            remCharButton.onClick.RemoveAllListeners();
                            remCharButton.onClick.AddListener(delegate { RemoveCharacterFromParty(remCharButton); });
                        }
                    }
                    else
                    {
                        //Remove listeners if inactive
                        Button remCharButton = remCharButtonGO.GetComponent<Button>();
                        if (remCharButton != null)
                        {
                            remCharButton.onClick.RemoveAllListeners();
                        }
                    }

                    stockButtons.transform.GetChild(2).GetComponent<TextMeshProUGUI>().text = character.name;
                    stockButtons.transform.GetChild(3).GetComponent<TextMeshProUGUI>().text = "Lv: " + character.level.ToString();
                    stockButtons.transform.GetChild(5).GetChild(0).GetComponent<Image>().fillAmount = (character.maxHP == 0) ? 1 : (float)character.hP / character.maxHP;
                    stockButtons.transform.GetChild(5).GetChild(1).GetChild(0).GetComponent<TextMeshProUGUI>().text = character.hP.ToString();
                    Types type1 = character.skills.GetHighestSpDef();
                    stockButtons.transform.GetChild(6).GetChild(0).GetComponent<Image>().sprite = GetTypeSprite(type1);
                    Types type2 = character.skills.GetHighestSpAtk();
                    stockButtons.transform.GetChild(7).GetChild(0).GetComponent<Image>().sprite = GetTypeSprite(type2);
                    Types type3 = character.skills.GetLowestSpDef();
                    stockButtons.transform.GetChild(8).GetChild(0).GetComponent<Image>().sprite = GetTypeSprite(type3);

                    if (isCharDead)
                    {
                        stockButtons.transform.GetChild(9).gameObject.SetActive(true);
                        stockButtons.transform.GetChild(10).gameObject.SetActive(true);
                        stockButtons.transform.GetChild(9).GetComponent<Image>().color = new Color(1f, 0f, 0f, 0.4f);
                        stockButtons.transform.GetChild(10).GetComponent<TextMeshProUGUI>().text = "DEAD";
                    }
                    else
                    {
                        stockButtons.transform.GetChild(9).GetComponent<Image>().color = new Color(0f, 1f, 0f, 0.4f);
                        stockButtons.transform.GetChild(10).GetComponent<TextMeshProUGUI>().text = "ACTIVE";
                    }
                }
                else // otherwise it hide the values
                {
                    stockButtons.transform.GetChild(0).gameObject.SetActive(true);
                    stockButtons.transform.GetChild(1).gameObject.SetActive(false);
                    stockButtons.transform.GetChild(2).gameObject.SetActive(false);
                    stockButtons.transform.GetChild(3).gameObject.SetActive(false);
                    stockButtons.transform.GetChild(5).gameObject.SetActive(false);
                    stockButtons.transform.GetChild(6).gameObject.SetActive(false);
                    stockButtons.transform.GetChild(7).gameObject.SetActive(false);
                    stockButtons.transform.GetChild(8).gameObject.SetActive(false);
                    stockButtons.transform.GetChild(9).gameObject.SetActive(false);
                    stockButtons.transform.GetChild(10).gameObject.SetActive(false);
                    stockButtons.transform.GetChild(11).gameObject.SetActive(false);
                }
            }
            // offsets the position of the party based on the selected party for eddition
            party.transform.position = transform.position + new Vector3(spacing * (party.transform.GetSiblingIndex() - partyIndex), 0f, 0f);
        }
        stickyOverlayForStock.RefreshStickyPartyUI();
    }

    void PartyNameEditted(string name, TMP_InputField text) // updates the party name
    {
        configsHandler.SetPartyName(text.transform.parent.GetSiblingIndex(), name);
        RefreshPartyUI(partyIndex);
    }

    public IEnumerator SlotSelected(Button button) // makes the button slot selected
    {
        yield return null;

        HighlightStickyButton(button); // also selects the button on sticky overlay

        // Check if this is a stock button click and we have an active slot selected
        bool isStockButton = IsStockButton(button);
        bool isActiveButton = IsActiveButton(button);

        if (isStockButton && HasSelectedActiveSlot())
        {
            PartyCharacters partyCharacters = configsHandler.GetPartyCharacters(partyIndex);
            int stockIndex = button.transform.GetSiblingIndex();

            if (partyCharacters.stockCharacters[stockIndex] != null)
            {
                // Move character only if not null
                MoveStockCharacterToActiveSlot(button);
                yield break;
            }
            else
            {
                // Stock character null, just select the stock button
                foreach (var item in transform.GetComponentsInChildren<Button>().Where(b => b.name != "remChar")) // disables all the other selected buttons overlay
                {
                    var sb1Image = item.GetComponentsInChildren<Image>().FirstOrDefault(it => it.name == "SB");
                    if (sb1Image != null)
                    {
                        sb1Image.enabled = false;
                    }
                }

                var sbImage = button.GetComponentsInChildren<Image>().FirstOrDefault(it => it.name == "SB");
                if (sbImage != null) sbImage.enabled = true;
                yield break;
            }
        }
        foreach (var item in transform.GetComponentsInChildren<Button>().Where(b => b.name != "remChar")) // disables all the other selected buttons overlay
        {
            var sbImage = item.GetComponentsInChildren<Image>().FirstOrDefault(it => it.name == "SB");
            if (sbImage != null)
            {
                sbImage.enabled = false;
            }
        }

        // enables the selected button overlay
        var selectedSbImage = button.GetComponentsInChildren<Image>().FirstOrDefault(it => it.name == "SB");
        //if (selectedSbImage != null)
        //{
            selectedSbImage.enabled = true;
        //}
    }

    bool IsStockButton(Button button)
    {
        // Check if the button is in the stock section (child 2)
        return button.transform.IsChildOf(partiesOBJ[partyIndex].transform.GetChild(2));
    }

    bool IsActiveButton(Button button)
    {
        // Check if the button is in the active section (child 1)
        return button.transform.IsChildOf(partiesOBJ[partyIndex].transform.GetChild(1));
    }

    bool HasSelectedActiveSlot()
    {
        // Check if there's a selected active slot that is null (empty)
        var activeButtons = partiesOBJ[partyIndex].transform.GetChild(1).GetChild(1).GetComponentsInChildren<Button>().Where(b => b.name != "remChar");

        foreach (var activeButton in activeButtons)
        {
            var sbImage = activeButton.GetComponentsInChildren<Image>().FirstOrDefault(it => it.name == "SB");
            if (sbImage != null && sbImage.enabled)
            {
                // Check if this active slot has a null character
                PartyCharacters partyCharacters = configsHandler.GetPartyCharacters(partyIndex);
                int slotIndex = activeButton.transform.GetSiblingIndex();
                return partyCharacters.activeCharacters[slotIndex] == null;
            }
        }
        return false;
    }

    void MoveStockCharacterToActiveSlot(Button stockButton)
    {
        // Find the selected active slot
        var activeButtons = partiesOBJ[partyIndex].transform.GetChild(1).GetChild(1).GetComponentsInChildren<Button>().Where(b => b.name != "remChar");

        foreach (var activeButton in activeButtons)
        {
            var sbImage = activeButton.GetComponentsInChildren<Image>().FirstOrDefault(it => it.name == "SB");
            if (sbImage != null && sbImage.enabled)
            {
                // Get the character from stock
                PartyCharacters partyCharacters = configsHandler.GetPartyCharacters(partyIndex);
                int stockSlotIndex = stockButton.transform.GetSiblingIndex();
                int activeSlotIndex = activeButton.transform.GetSiblingIndex();

                BattleCharacter stockCharacter = partyCharacters.stockCharacters[stockSlotIndex];

                if (stockCharacter != null)
                {
                    // Check if character is already in active party and remove it first
                    for (int i = 0; i < partyCharacters.activeCharacters.Length; i++)
                    {
                        if (partyCharacters.activeCharacters[i] == stockCharacter)
                        {
                            configsHandler.SetCharacterToParty(partyIndex, i, true, null);
                            break;
                        }
                    }

                    // Move character from stock to active
                    configsHandler.SetCharacterToParty(partyIndex, activeSlotIndex, true, stockCharacter);

                    // Clear the selection
                    sbImage.enabled = false;
                }
                break;
            }
        }
        RefreshPartyUI(partyIndex);

    }

    public void RemoveCharacterFromParty(Button button) // removes the character from the party
    {
        int slotIndex = button.transform.parent.GetSiblingIndex(); // gets the slot index
        bool isActive = button.transform.parent.name.StartsWith("AP"); // gets if the character is active or not

        configsHandler.SetCharacterToParty(partyIndex, slotIndex, isActive, null);
        RefreshPartyUI(partyIndex);
    }

    public void HighlightStickyButton(Button clickedStockButton)
    {
        // Get index among non-"remChar" buttons
        Transform stockParent = clickedStockButton.transform.parent;
        var buttons = stockParent.GetComponentsInChildren<Button>().Where(b => b.name != "remChar").ToList();
        int index = buttons.IndexOf(clickedStockButton);
        if (index < 0) return;

        // Reference to the StickyOverlayForStock GameObject (assign this if not accessible globally)
        //GameObject stickyOverlay = /* assign your StickyOverlayForStock reference */;

        // Get corresponding sticky button
        var stickyButtons = stickyOverlayForStock.GetComponentsInChildren<Button>().Where(b => b.name != "remChar").ToList();
        if (index >= stickyButtons.Count) return;

        // Turn off all sbImage overlays first
        foreach (var button in stickyButtons)
        {
            var sb1Image = button.GetComponentsInChildren<Image>().FirstOrDefault(it => it.name == "SB");
            if (sb1Image != null)
                sb1Image.enabled = false;
        }

        // Enable the sbImage of the matching sticky button
        var matchingStickyButton = stickyButtons[index];
        var sbImage = matchingStickyButton.GetComponentsInChildren<Image>().FirstOrDefault(it => it.name == "SB");
        if (sbImage != null)
            sbImage.enabled = true;
    }

    void LoadAllTypeSprites()
    {
        foreach (Types type in System.Enum.GetValues(typeof(Types)))
        {
            if (!typeSpriteCache.ContainsKey(type))
            {
                Sprite sprite = Resources.Load<Sprite>($"Sprites/BattleEffects/{type}");
                if (sprite != null)
                {
                    typeSpriteCache[type] = sprite;
                }
                else
                {
                    Debug.LogWarning($"Missing sprite for type: {type}");
                }
            }
        }
    }

    Sprite GetTypeSprite(Types type)
    {
        if (typeSpriteCache.TryGetValue(type, out Sprite sprite))
        {
            return sprite;
        }
        else
        {
            Debug.LogWarning($"Sprite not found for type: {type}");
            return null;
        }
    }

}
