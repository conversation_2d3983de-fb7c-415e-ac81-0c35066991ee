using TMPro;
using UnityEngine;

public class ComboChance : MonoBehaviour
{
    private int index; // Index of the combo chance in the array
    private ConfigsHandler configsHandler; // Reference to the ConfigsHandler
    private TMP_InputField input; // Input field for editing

    void Start()
    {
        // Get index from last character of the GameObject's name
        if (!int.TryParse(name[^1].ToString(), out index))
        {
            Debug.LogError($"Invalid index in GameObject name: {name}");
            return;
        }

        // Get reference to ConfigsHandler
        configsHandler = GameObject.Find("GameConfigsHandler")?.GetComponent<ConfigsHandler>();
        if (configsHandler == null)
        {
            Debug.LogError("GameConfigsHandler not found in scene.");
            return;
        }

        // Get TMP_InputField component
        input = GetComponent<TMP_InputField>();
        if (input == null)
        {
            Debug.LogError("TMP_InputField component missing.");
            return;
        }

        // Initialize input field value
        if (index < configsHandler.ComboChance.Length)
        {
            input.text = configsHandler.ComboChance[index].ToString();
        }
        else
        {
            Debug.LogWarning($"ComboChance index {index} out of bounds.");
        }

        // Add listener to update value when editing ends
        input.onEndEdit.AddListener(OnEndEdit);
    }

    private void OnEndEdit(string value)
    {
        if (int.TryParse(value, out int result))
        {
            configsHandler.UpdateComboChance(index, result);
        }
        else
        {
            Debug.LogWarning($"Invalid input for ComboChance: {value}");
            input.text = configsHandler.ComboChance[index].ToString(); // Revert invalid input
        }
    }
}
