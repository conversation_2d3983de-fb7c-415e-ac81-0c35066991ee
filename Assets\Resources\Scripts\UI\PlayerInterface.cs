using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using System;

public class PlayerInterface : <PERSON>oBeh<PERSON>our, IPointerClickHandler, IPointerDownHandler, IPointerUpHandler
{
    public GameObject ConfigHandlerOBJ; // Reference to the ConfigsHandler GameObject
    public GameObject Button; // Reference to the Button GameObject of the player interface
    public GameObject valueOBJ;

    public Image AttackCooldown; // Reference to the Image to show the attack cooldown

    public BattleCharacter pC; // Reference to the player character

    public bool didCrit = false;

    ConfigsHandler configsHandler; // Reference to the ConfigsHandler script

    Image healthBar;
    Image healthBarDelay;

    TextMeshProUGUI healthAmount;

    int playerIndex; // Index of the player

    // Click handling variables
    private float pointerDownTime;
    private bool isPointerDown = false;
    private bool longPressTriggered = false;
    private float lastClickTime = 0f;
    private int clickCount = 0;

    [Header("Click Settings")]
    public float longPressThreshold = 0.25f; // Time that the button needs to be pressed to trigger long press
    public float doubleClickThreshold = 0.3f; // Maximum time between clicks for double click

    private readonly float animationDuration = 1.5f;
    private Tween delayTween;

    Image gotHit;

    void Start()
    {
        playerIndex = int.Parse(name[^1..]); // Get the index of the player

        configsHandler = ConfigHandlerOBJ.GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script

        healthBar = transform.GetChild(2).GetChild(1).GetComponent<Image>();
        healthBarDelay = transform.GetChild(2).GetChild(0).GetComponent<Image>();
        healthAmount = transform.GetChild(2).GetChild(2).GetChild(0).GetComponent<TextMeshProUGUI>();

        gotHit = transform.GetChild(7).GetComponent<Image>();
    }

    void Update()
    {
        Color currentColor = GetComponent<Image>().color; // gets the current color of the button
        UpdateStatusIcons(); // updates the status icons of the player

        pC = configsHandler.GetPlayerCharacter(playerIndex); // Get the player character


        if (GetComponent<Image>().sprite != Resources.Load<Sprite>("Sprites/UI/CharBuIdleT")) GetComponent<Image>().sprite = Resources.Load<Sprite>("Sprites/UI/CharBuIdleT");

        if (pC != null && !pC.IsDead) // checks if the player is alive or if it exists
        {

            if (transform.GetChild(5).gameObject.activeSelf) transform.GetChild(5).gameObject.SetActive(false); // hides the dead icon if the player isn't dead

            // changes the color of the button smoothly to white if the color is not white or the other two colors, which represents if the player is dead or if the player doesn't exist
            if (currentColor != new Color(1f, 0f, 0f, 0.9960784f)) GetComponent<Image>().color = Tools.MoveTowardsColor(currentColor, Color.black, Time.deltaTime * 2f);
            else GetComponent<Image>().color = Color.black;

            // Reduce the attack cooldown of the player using its speed as a percentage
            if (AttackCooldown.fillAmount > 0.0001f) AttackCooldown.fillAmount -= Mathf.Max(1, pC.mods.GetSpeed()) / 100f * Time.deltaTime;



            // Updates the player interface values
            transform.GetChild(2).gameObject.SetActive(true); // health bar
            transform.GetChild(3).gameObject.SetActive(true); // name
            transform.GetChild(6).gameObject.SetActive(pC.isStunned);


            healthBar.fillAmount = (pC.maxHP == 0) ? 1 : (float)pC.hP / pC.maxHP; // updates the health bar amount with the current health
            float targetFillAmount = (pC.maxHP == 0) ? 1 : (float)pC.hP / pC.maxHP;
            UpdateHealthBarDelay(targetFillAmount); // updates the health bar delay amount with the current health
            healthBar.color =
                Tools.InterpolateColor(
                    new(178f / 255f, 250f / 255f, 67f / 255f),
                    Color.red,
                    healthBar.fillAmount,
                    5f);

            healthAmount.text = pC.hP.ToString(); // updates the health text with the current health

            transform.GetChild(3).GetComponent<TextMeshProUGUI>().text = pC.name; // updates the name text with the current name
        }
        else
        {
            if (pC != null && pC.IsDead)
            {
                transform.GetChild(5).GetComponent<TextMeshProUGUI>().text = "DEAD"; // sets the dead text to the dead icon
                if (!transform.GetChild(5).gameObject.activeSelf) transform.GetChild(5).gameObject.SetActive(true); // shows the dead icon if the player is dead
                GetComponent<Image>().color = new Color(1f, 0f, 0f, 0.9960784f);
            }
            else
            {
                transform.GetChild(5).GetComponent<TextMeshProUGUI>().text = "EMPTY"; // sets the dead text to the dead icon
                GetComponent<Image>().color = Color.black;
            }

            // removes the cooldown
            AttackCooldown.fillAmount = 0;

            // hides the health bar and the name
            transform.GetChild(2).gameObject.SetActive(false);
            transform.GetChild(3).gameObject.SetActive(false);
            transform.GetChild(5).gameObject.SetActive(true);
            transform.GetChild(6).gameObject.SetActive(false);
        }

        // Handle long press detection in Update
        if (isPointerDown && !longPressTriggered)
        {
            if (Time.time - pointerDownTime >= longPressThreshold)
            {
                longPressTriggered = true;
                OnLongPress();
            }
        }

        // changes the sprite of the button's border, its not perfect but is better than have the sprite over it when the player is hitted
        // there is way to much orange in the effects, letting the sprite over it makes the hitted effect look orange instead of red
        if (currentColor == new Color(1f, 0f, 0f, 0.9960784f) || currentColor == Color.gray || currentColor == new Color(0, 0, 0, 1))
            transform.GetChild(0).GetComponent<Image>().sprite = Resources.Load<Sprite>("Sprites/UI/CharBuIdleT1");
        else
            transform.GetChild(0).GetComponent<Image>().sprite = Resources.Load<Sprite>("Sprites/UI/CharBuF");

        // shows the selected highlight if the player is selected
        if (pC != null && !pC.IsDead) transform.GetChild(0).GetComponent<Image>().enabled = configsHandler.GetPlayerSelected(playerIndex);
        else transform.GetChild(0).GetComponent<Image>().enabled = false;

        // if the previous is player is null or dead, select the player
        if (!configsHandler.IsAnyPlayerSelected() && (configsHandler.GetPlayerCharacter(Math.Max(0, playerIndex - 1)) == null || configsHandler.GetPlayerCharacter(Math.Max(0, playerIndex - 1)).IsDead))
            PlayerGotSelected();

        // Hides the player sprite if the player is dead or doesn't exist
        transform.GetChild(0).GetChild(0).gameObject.SetActive(pC != null && !pC.IsDead);

    }



    void UpdateHealthBarDelay(float targetFillAmount)
    {
        // Kill existing tween to prevent overlapping
        delayTween?.Kill();

        // Start smooth animation with ease-in
        delayTween = healthBarDelay.DOFillAmount(targetFillAmount, animationDuration)
            .SetEase(Ease.OutQuad);
    }

    IEnumerator ShowSkillValues() // Coroutine to show the values of the player
    {
        yield return null;
        yield return null;

        valueOBJ.SetActive(true); // Set the EnemyValues GameObject to active
        float x = 0;
        float y = 2;

        valueOBJ.transform.position = new Vector3(x, y, transform.position.z) + new Vector3(0f, 0f, -1.25f); // Set the position of the EnemyValues GameObject to the enemy position

        valueOBJ.GetComponent<EnemyValues>().UpdateCharacter(configsHandler.GetPlayerCharacter(playerIndex)); // Makes the EnemyValues GameObject show the values of the enemy
    }

    bool PlayerGotSelected()
    {
        bool canSelectPlayer;

        canSelectPlayer = pC != null && !pC.IsDead && !pC.isStunned && AttackCooldown.fillAmount <= 0.0001f && configsHandler.playerTurn;

        if (canSelectPlayer) configsHandler.SetPlayerSelected(playerIndex); // selects the player
        return canSelectPlayer;
    }

    #region Pointer Event Handlers

    public void OnPointerDown(PointerEventData eventData)
    {
        if (eventData.button != PointerEventData.InputButton.Left)
            return;

        isPointerDown = true;
        pointerDownTime = Time.time;
        longPressTriggered = false;
    }

    public void OnPointerUp(PointerEventData eventData)
    {
        if (eventData.button != PointerEventData.InputButton.Left)
            return;

        isPointerDown = false;

        // If long press was triggered, don't process click
        if (longPressTriggered)
        {
            longPressTriggered = false;
            return;
        }

        // Check if it was a quick release (not a long press)
        float pressDuration = Time.time - pointerDownTime;
        if (pressDuration < longPressThreshold)
        {
            HandleClick();
        }
    }

    public void OnPointerClick(PointerEventData eventData)
    {
        // This is called by Unity's event system, but we handle clicks in OnPointerUp
        // to have better control over timing with long press detection
    }

    #endregion

    #region Click Handling Methods

    private void HandleClick()
    {
        float currentTime = Time.time;

        // Check for double click
        if (currentTime - lastClickTime <= doubleClickThreshold)
        {
            clickCount++;
            if (clickCount >= 2)
            {
                // Double click detected - cancel any pending single click and execute double click
                StopAllCoroutines(); // Stop any pending single click
                OnDoubleClick();
                clickCount = 0;
                lastClickTime = 0f;
                return;
            }
        }
        else
        {
            clickCount = 1;
        }

        lastClickTime = currentTime;

        // Execute single click immediately
        OnSingleClick();

        // Start coroutine to potentially override with double click
        StartCoroutine(HandleDoubleClickWindow());
    }

    private IEnumerator HandleDoubleClickWindow()
    {
        yield return new WaitForSeconds(doubleClickThreshold);

        // Reset click count after double click window expires
        if (clickCount == 1)
        {
            clickCount = 0;
        }
    }

    private void OnSingleClick()
    {
        if (!configsHandler.ActionMenu.activeSelf)
        {
            // play the select sound effect
            bool canSelectPlayer;
            canSelectPlayer = pC != null && !pC.IsDead && !pC.isStunned && AttackCooldown.fillAmount <= 0.0001f && configsHandler.playerTurn;
            ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/" + (canSelectPlayer ? "Select" : "RejectedSelect")));
            PlayerGotSelected();
        }
    }

    private void OnDoubleClick()
    {
        if (!configsHandler.ActionMenu.activeSelf)
        {
            // For double click, we want to show skill values regardless of selection state
            // The first click already selected the player, so now we show values
            if (!valueOBJ.activeSelf && pC != null && !pC.IsDead)
            {
                StartCoroutine(ShowSkillValues());
            }
        }
    }

    private void OnLongPress()
    {
        if (!configsHandler.ActionMenu.activeSelf)
        {
            if (PlayerGotSelected())
            {
                configsHandler.ActionMenu.SetActive(true);
                configsHandler.ActionMenu.GetComponent<ActionMenu>().active = true;
                configsHandler.ActionMenu.transform.position = transform.position - new Vector3(0, 1.6f, 0.1f);
            }
        }
    }

    #endregion

    // changes the color of the button to show that the player was hitted,
    // the WaitForSeconds is to make the effect being aplicated after
    // the damage star to the player interface
    public IEnumerator GotHitted()
    {
        yield return new WaitForSeconds(0.1f);
        AttackCooldown.fillAmount = 0;
        gotHit.gameObject.SetActive(true);
        yield return new WaitForSeconds(0.2f);
        gotHit.gameObject.SetActive(false);
    }

    public void AddStatusIcon(Sprite icon, string name) // adds a status icon to the player interface
    {
        BattleCharacter temp = configsHandler.GetCharacterByID(pC.id); // gets the player character

        temp.statusIcons.Add(name, new StatusIcon(icon, name, gameObject)); // adds the status icon to the player character

        configsHandler.SetCharacter(temp, configsHandler.GetCharacter(pC)); // updates the player character
    }

    public void RemoveStatusIcon(string name) // removes a status icon from the player interface
    {
        BattleCharacter temp = configsHandler.GetCharacterByID(pC.id); // gets the player character
        temp.statusIcons[name].icon.GetComponent<SkillIconUpdate>().remove = true; // sets the remove bool to true to remove the icon
        configsHandler.SetCharacter(temp, configsHandler.GetCharacter(pC)); // updates the player character
    }

    public void UpdateStatusIcons() // updates the status icons of the player interface
    {
        if (pC == null) return; // returns if the player character is null
        int counter = 0;
        foreach (KeyValuePair<string, StatusIcon> icon in pC.statusIcons) // loops through all the status icons of the player character and updates their position
        {
            icon.Value.icon.transform.position = transform.position + new Vector3((pC.statusIcons.Count - 1) * -0.125f + counter * 0.25f, 0.6f, 0f);
            counter++;
        }
    }
}

public class StatusIcon // Class to handle the status icons of the player interface
{
    public GameObject icon; // Reference to the icon GameObject

    public StatusIcon(Sprite icon, string name, GameObject parent) // Constructor to create the status icon
    {
        GameObject iconPrefab = Resources.Load<GameObject>("Prefabs/SkillIcon"); // Load the icon prefab

        this.icon = UnityEngine.Object.Instantiate(iconPrefab, parent.transform); // Instantiate the icon prefab

        this.icon.GetComponent<Image>().sprite = icon; // Set the icon sprite
        this.icon.name = name; // Set the icon name
    }
}