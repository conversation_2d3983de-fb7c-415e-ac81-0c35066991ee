using DG.Tweening;
using Spine.Unity;
using System.Collections;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class EnemyAnimation : MonoBehaviour
{
    public GameObject enemyAnimOBJ, healthBarOBJ;

    public SkeletonAnimation enemyAnimation;

    public float speed = 1; // speed of the animation
    public float spacing = 2; // spacing of the animation
    public float timeDelay = 0; // delay of the animation

    public float spaceOffset = 0.01f;

    public float treshold = 0.25f;
    float time = 0;

    EnemyInterface enemyInterface;

    ConfigsHandler configsHandler;

    public bool gotHitted = false;

    [Range(0, 3)]
    public int enemyNum; // number of the enemy

    int enemyOrder;

    public Vector3 centralPos, startPos; // start position

    public Vector3 offset, respawnOffset; // offset of the shaking animation

    Vector3 initialAnimationScale, initialScale, initialHealthBarScale;

    void Start()
    {
        enemyInterface = GetComponent<EnemyInterface>();

        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();

        healthBarOBJ = transform.GetChild(0).GetChild(2).gameObject;

        enemyAnimOBJ = transform.GetChild(0).GetChild(0).gameObject; // gats the gameObject of the animation

        initialAnimationScale = enemyAnimOBJ.transform.localScale;

        enemyAnimation = enemyAnimOBJ.GetComponent<SkeletonAnimation>();

        enemyOrder = transform.GetSiblingIndex();

        initialScale = transform.localScale;
        initialHealthBarScale = healthBarOBJ.transform.localScale;

        healthBarOBJ.transform.localScale = Vector3.zero;

        startPos = transform.position; // sets the start position
        centralPos = startPos;
        respawnOffset = centralPos;
    }

    void Update()
    {
        if (enemyInterface.eC != null)
        {
            SkeletonDataAsset animation = Resources.Load<SkeletonDataAsset>("Spine/" + enemyInterface.eC.name + "/" + enemyInterface.eC.name + "_SkeletonData");

            if (animation != null && enemyAnimation.skeletonDataAsset != animation)
            {
                enemyAnimation.skeletonDataAsset = animation;

                enemyAnimation.Initialize(true);

                enemyAnimation.AnimationState.SetAnimation(0, "Idle", true);
            }
        }


        healthBarOBJ.transform.localScale = enemyInterface.IsHPDepliding() || time > 0 ? initialHealthBarScale * (initialScale.x / transform.localScale.x) : Vector3.MoveTowards(healthBarOBJ.transform.localScale, Vector3.zero, Time.deltaTime * 10f / transform.localScale.x);

        float value = (initialHealthBarScale * (initialScale.x / transform.localScale.x)).x;
        float alpha = Tools.NormalizeValue(value * 0.25f, value * 0.5f, healthBarOBJ.transform.localScale.x);

        healthBarOBJ.GetComponent<Image>().material.color = new(1, 1, 1, alpha);
        healthBarOBJ.transform.GetChild(2).GetChild(0).GetComponent<TextMeshProUGUI>().color = new(1, 1, 1, alpha);
        healthBarOBJ.transform.GetChild(3).GetComponent<TextMeshProUGUI>().color = new(0.6980392f, 0.9803922f, 0.2627451f, alpha);



        if (time >= treshold) time = 0;

        if (enemyInterface.IsHPDepliding()) time = Time.deltaTime;

        if ((time > 0 && time < treshold) || enemyInterface.IsHPDepliding()) time += Time.deltaTime;

        if (configsHandler.GetNumberOfEnemies() == 1 && centralPos != new Vector3(0.3f, 1.5f, startPos.z))
        {
            centralPos = new(0.3f, 1.5f, startPos.z);
            respawnOffset = centralPos;
            if (transform.localScale != Vector3.zero) transform.localScale = new(13f, 13f, 1);
        }
        else if (configsHandler.GetNumberOfEnemies() == 2 && centralPos != new Vector3(1.5f, 1.8f, startPos.z + spaceOffset) && centralPos != new Vector3(-0.7f, 1.8f, startPos.z))
        {
            centralPos = enemyOrder == 0 ? new(-0.7f, 1.8f, startPos.z) : new(1.5f, 1.8f, startPos.z + spaceOffset);
            respawnOffset = centralPos;
            if (transform.localScale != Vector3.zero) transform.localScale = enemyOrder == 0 ? new(9.7f, 9.7f, 1) : new Vector3(9.7f, 9.7f, 1) * 0.9f;
        }
        else if (configsHandler.GetNumberOfEnemies() == 3
            &&
            centralPos != new Vector3(1.5f, 1.4f, startPos.z)
            &&
            centralPos != new Vector3(-1.5f, 1.4f, startPos.z)
            &&
            centralPos != new Vector3(0f, 2.4f, startPos.z + spaceOffset))
        {
            if (enemyOrder != 1)
            {
                centralPos = enemyOrder == 0 ? new(0, 2.4f, startPos.z + spaceOffset) : (new(-1.5f, 1.4f, startPos.z));
            }
            else
            {
                centralPos = enemyOrder == 0 ? new(0, 2.9f, startPos.z + spaceOffset) : (new(1.5f, 1.4f, startPos.z));
            }
            respawnOffset = centralPos;
            if (transform.localScale != Vector3.zero) transform.localScale = new Vector3(7.2f, 7.2f, 1) * (enemyOrder == 0 ? 0.9f : 1f);
        }
        else if (configsHandler.GetNumberOfEnemies() == 4 && centralPos != startPos + (enemyOrder == 0 || enemyOrder == 3 ? new Vector3(0, 0, spaceOffset) : Vector3.zero))
        {
            centralPos = startPos + (enemyOrder == 0 || enemyOrder == 3 ? new Vector3(0, 0, spaceOffset) : Vector3.zero);
            respawnOffset = centralPos;
            if (transform.localScale != Vector3.zero) transform.localScale = enemyOrder == 0 || enemyOrder == 3 ? initialScale * 0.9f : initialScale;
        }


        respawnOffset = Vector3.Lerp(respawnOffset, centralPos, Time.deltaTime * 2f);
        if (speed <= 0) speed = 1f; // if speed is 0 or less, set it to 1
        float y = Mathf.Sin((Time.time + timeDelay * enemyNum) * speed) * spacing; // calculate the y position at the current time
        transform.position = respawnOffset + new Vector3(0f, y, 0f) + offset; // set the position
    }

    public IEnumerator HitAnimation(float duration) // animation of when the enemy is hitted
    {
        GetComponent<EnemyInterface>().AttackCooldown.fillAmount = 0; // resets the cooldown

        gotHitted = true;

        for (int i = 0; i <= duration; i++) // loop that makes the enemy blink by an amount of frams difined by the duration
        {
            enemyAnimOBJ.transform.localScale = (enemyAnimOBJ.transform.localScale.x < initialAnimationScale.x / 2) ? initialAnimationScale : Vector3.zero; // depending the curent scale, it divedes it by 4 (same thing to multiply by 0.25) or multiples it by 4
            yield return new WaitForSeconds((duration / 60f) / duration); // waits the same amount that one frame would take at 60fps
        }

        gotHitted = false;

        enemyAnimOBJ.transform.localScale = initialAnimationScale; // resets the scale
    }
}
