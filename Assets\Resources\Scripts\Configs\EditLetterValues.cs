using TMPro;
using UnityEngine;

public class EditLetterValues : MonoBehaviour
{
    public string letter = "B";

    ConfigsHandler configsHandler; // Reference to the ConfigsHandler

    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script

        GetComponent<TMP_InputField>().onEndEdit.AddListener(EndEditValue); // Add the listener to the TMP_InputField to edit the value
        switch (letter)
        {
            case "B": GetComponent<TMP_InputField>().text = configsHandler.B.ToString(); break;
            case "C": GetComponent<TMP_InputField>().text = configsHandler.C.ToString(); break;
            case "D": GetComponent<TMP_InputField>().text = configsHandler.D.ToString(); break;
            case "E": GetComponent<TMP_InputField>().text = configsHandler.E.ToString(); break;
            case "F": GetComponent<TMP_InputField>().text = configsHandler.F.ToString(); break;
            case "G": GetComponent<TMP_InputField>().text = configsHandler.G.ToString(); break;
        }
    }

    void EndEditValue(string arg)
    {
        switch (letter) // Edit the value of the letter
        {
            case "B": configsHandler.B = float.Parse(arg); break;
            case "C": configsHandler.C = float.Parse(arg); break;
            case "D": configsHandler.D = float.Parse(arg); break;
            case "E": configsHandler.E = float.Parse(arg); break;
            case "F": configsHandler.F = float.Parse(arg); break;
            case "G": configsHandler.G = float.Parse(arg); break;
        }

        configsHandler.SaveValues(); // Save the values
    }
}
