using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using Unity.VisualScripting;
using System.Collections;

public class CharVirtualizationController : MonoBehaviour
{
    ConfigsHandler configsHandler; // the ConfigsHand<PERSON> script

    public RectTransform scrollContent;
    public ScrollRect scrollRect;
    public CharPool charPool;
    public int totalCharacters;
    public float itemHeight;
    public float viewportHeight;

    private Dictionary<int, GameObject> visibleCharacters = new();

    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // gets the ConfigsHandler script

        totalCharacters = configsHandler.GetCharacters().Count;

        // Calculate itemHeight from prefab
        if (charPool.characterPrefab != null)
        {
            var rect = charPool.characterPrefab.GetComponent<RectTransform>();
            itemHeight = rect.rect.height;
        }
        else
        {
            Debug.LogError("Character prefab not assigned in CharPool!");
        }

        // Set the scroll content height for virtualization
        scrollContent.sizeDelta = new Vector2(
        scrollContent.sizeDelta.x,
        totalCharacters * itemHeight
        );


        scrollRect.onValueChanged.AddListener((v) =>
        {
            UpdateVisibleCharacters();
        });

        // Delay the initial update to ensure layout is ready
        StartCoroutine(DelayedInitialUpdate());
    }

    private IEnumerator DelayedInitialUpdate()
    {
        yield return null; // Wait one frame
        UpdateVisibleCharacters();
    }

    private void UpdateVisibleCharacters()
    {

        // Calculate viewportHeight from the ScrollRect's viewport
        viewportHeight = scrollRect.viewport.rect.height;

        float scrollY = scrollContent.anchoredPosition.y;
        int firstVisible = Mathf.Max(0, Mathf.FloorToInt(scrollY / itemHeight));
        int visibleCount = Mathf.CeilToInt(viewportHeight / itemHeight) + 1;
        int lastVisible = Mathf.Min(totalCharacters - 1, firstVisible + visibleCount);

        // Hide and return pooled objects for characters that are no longer visible
        List<int> toRemove = new(visibleCharacters.Keys);
        foreach (int key in toRemove)
        {
            if (key < firstVisible || key > lastVisible)
            {
                charPool.ReturnToPool(visibleCharacters[key]);
                visibleCharacters.Remove(key);
            }
        }

        // Show new visible characters
        for (int i = firstVisible; i <= lastVisible; i++)
        {
            if (!visibleCharacters.ContainsKey(i))
            {
                GameObject obj = charPool.GetPooledObject();
                if (obj != null)
                {
                    obj.transform.SetParent(scrollContent, false);
                    var battleChar = GetBattleCharacter(i);
                    obj.GetComponent<CharConfUI>().character = battleChar;
                    obj.GetComponent<CharConfUI>().UpdateUI();
                    obj.name = "Char_" + i + "_" + battleChar.name;
                    obj.SetActive(true);
                    visibleCharacters[i] = obj;
                }
                else
                {
                    Debug.LogWarning("[CharVirtualizationController] No pooled object available!");
                }
            }
            // Set position for all visible objects (new or already active)
            if (visibleCharacters.TryGetValue(i, out var go))
            {
                var rect = go.GetComponent<RectTransform>();

                //Center the items in the scroll view
                float centerX = scrollContent.rect.width / 2 - rect.rect.width / 2;
                float y = -i * itemHeight;
                rect.anchoredPosition = new Vector2(centerX, y);
            }
        }
    }

    private BattleCharacter GetBattleCharacter(int index)
    {
        return configsHandler.GetCharacter(index);
    }
}
