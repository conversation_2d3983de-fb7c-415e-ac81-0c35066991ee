using UnityEngine;
using UnityEngine.UI;

public class SkillIconUpdate : MonoBehaviour
{
    ConfigsHandler configsHandler; // Reference to the ConfigsHandler
    BattleCharacter character; // Reference to the character
    PlayerInterface playerInterface; // Reference to the PlayerInterface

    readonly int numOfTurns = 1; // Number of turns the icon will last

    int startTurn; // The turn the icon was created

    public bool remove = false; // If the icon should be removed

    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script

        playerInterface = transform.parent.GetComponent<PlayerInterface>(); // Get the PlayerInterface script

        character = configsHandler.GetCharacterByID(playerInterface.pC.id); // Get the character

        startTurn = configsHandler.enemyTurns; // Get the current turn
    }


    void Update()
    {
        if ((startTurn + numOfTurns < configsHandler.enemyTurns && name != "Guarding") || character.IsDead) remove = true; // If the icon should be removed

        character = configsHandler.GetCharacterByID(character.id); // Get the character

        if (character == null || !character.statusIcons.TryGetValue(transform.name, out _)) Destroy(gameObject); // If the character is null or the icon is not in the character status icons, destroy the icon
        else if(!remove) // If the icon should not be removed
        {
            if (playerInterface.pC == null) gameObject.GetComponent<Image>().enabled = false; // If the player character is null, disable the icon
            else gameObject.GetComponent<Image>().enabled = true; // Otherwise enable the icon


            if (playerInterface.pC != character) // If the player character is not the character
            {
                foreach (var parent in transform.parent.parent.GetComponentsInChildren<PlayerInterface>()) // Get the player interface of the character
                {
                    if (parent.pC != null && parent.pC.statusIcons.TryGetValue(transform.name, out _)) // If the player interface has the icon
                    {
                        transform.SetParent(parent.transform); // Set the parent of the icon to the player interface
                        playerInterface = parent; // Set the player interface to the parent
                        parent.UpdateStatusIcons(); // Update the status icons of the parent
                        break; // Break the loop
                    }
                }
            }
        }

        if (remove) transform.localScale *= 0.9f; // If the icon should be removed, reduce the scale
        if (transform.localScale.x < 0.01f) // If the scale is less than 0.01
        {
            character.statusIcons.Remove(transform.name); // Remove the icon from the character status icons
        }
    }
}
