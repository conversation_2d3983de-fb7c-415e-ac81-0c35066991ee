using TMPro;
using Unity.VisualScripting;
using UnityEngine;

public class HealLabel : MonoBehaviour
{
    public static HealLabel Instance;
    ConfigsHandler configsHandler;
    public Canvas canvas;
    public Camera mainCamera;
    int enemyIndex;
    int playerIndex;

    public GameObject healLabelPrefab;
    // Enemies
    public GameObject enemy0;
    GameObject healthBarEnemy0;
    public GameObject enemy1;
    GameObject healthBarEnemy1;
    public GameObject enemy2;
    GameObject healthBarEnemy2;
    public GameObject enemy3;
    GameObject healthBarEnemy3;
    GameObject enemyPositionReference;
    // Players
    public GameObject player0;
    public GameObject player1;
    public GameObject player2;
    public GameObject player3;
    GameObject playerPositionReference;

    TextMeshProUGUI healText;
    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        Instance = this;
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();

        healthBarEnemy0 = enemy0.transform.GetChild(0).GetChild(2).gameObject;
        healthBarEnemy1 = enemy1.transform.GetChild(0).GetChild(2).gameObject;
        healthBarEnemy2 = enemy2.transform.GetChild(0).GetChild(2).gameObject;
        healthBarEnemy3 = enemy3.transform.GetChild(0).GetChild(2).gameObject;
    }

    // Update is called once per frame
    void Update()
    {
        // Always gets the enemy selected
        enemyIndex = configsHandler.GetSelectedEnemy();

        switch (enemyIndex) // Updates the position of where healLabel should appear, makes sure it appears around the enemy healthBar
        {
            case 0:
                enemyPositionReference = healthBarEnemy0;
                break;
            case 1:
                enemyPositionReference = healthBarEnemy1;
                break;
            case 2:
                enemyPositionReference = healthBarEnemy2;
                break;
            case 3:
                enemyPositionReference = healthBarEnemy3;
                break;
        }

        // Always gets the attacked player
        playerIndex = configsHandler.attackedPlayer;

        switch (playerIndex)
        {
            case 0:
                playerPositionReference = player0;
                break;
            case 1:
                playerPositionReference = player1;
                break;
            case 2:
                playerPositionReference = player2;
                break;
            case 3:
                playerPositionReference = player3;
                break;
        }

    }

    public void ShowHeal(long healAmount, BattleCharacter battleCharacter)
    {
        GameObject healLabel = Instantiate(healLabelPrefab, canvas.transform);

        // 2. Convert world position to screen point
        Vector3 screenPos;
        if (battleCharacter.isEnemy) { screenPos = mainCamera.WorldToScreenPoint(enemyPositionReference.transform.position); }
        else { screenPos = mainCamera.WorldToScreenPoint(playerPositionReference.transform.position); }

        // 3. Convert screen position to canvas local position
        RectTransform canvasRect = canvas.GetComponent<RectTransform>();
        Vector2 anchoredPos;
        RectTransformUtility.ScreenPointToLocalPointInRectangle(canvasRect, screenPos, mainCamera, out anchoredPos);

        // 4. Set the UI element's position
        RectTransform labelRect = healLabel.GetComponent<RectTransform>();
        labelRect.anchoredPosition = anchoredPos;
        if (battleCharacter.isEnemy) labelRect.anchoredPosition += new Vector2(50f, 150f);
        else labelRect.anchoredPosition += new Vector2(50f, 25f);


        healText = healLabel.GetComponent<TextMeshProUGUI>();
        healText.text = "+" + healAmount.ToString();
    }
}
