using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class EditFractionMod : MonoBehaviour
{
    private ConfigsHandler configsHandler; // Reference to the ConfigsHandler
    private TMP_InputField text;

    public GameObject labelButton;

    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script

        text = GetComponent<TMP_InputField>(); // Get the TMP_InputField component

        text.text = configsHandler.ModFraction.ToString();

        text.onEndEdit.AddListener(EditValue); // Add the listener to the TMP_InputField to edit the value

        labelButton.GetComponent<Button>().onClick.AddListener(() => ShowInfo());
    }

    void EditValue(string arg) // Edit the value of the energy timer
    {
        if (int.Parse(arg) < 1) arg = "1";

        text.text = arg;

        configsHandler.ModFraction = int.Parse(arg); // Set the energy timer to the new value
        configsHandler.SaveValues(); // Save the value
    }

        private void ShowInfo()
    {
        if (!labelButton.transform.GetChild(0).gameObject.activeSelf) labelButton.transform.GetChild(0).gameObject.SetActive(true); else labelButton.transform.GetChild(0).gameObject.SetActive(false);

    }
}
