using UnityEngine;

public class Add<PERSON><PERSON>cter // This is only If you and to show a character outside of ScrollView, most of the time this script is not needed
                        // Im keeping the script just in case
{

    readonly ConfigsHandler configsHandler;

    public AddCharacter(ConfigsHandler cH)
    {
        configsHandler = cH;

    }

    // Call this when you want to show a character at a given index
    public void Show<PERSON><PERSON>cter(int index, GameObject pooledObject)
    {
        // Set the name
        pooledObject.name = "Char " + index + "_" + configsHandler.GetCharacter(index).name;

        // Set the character data
        pooledObject.GetComponent<CharConfUI>().character = configsHandler.GetCharacter(index);

        // Set the position (assuming top-left anchor/pivot and vertical list)
        var rect = pooledObject.GetComponent<RectTransform>();
        rect.anchoredPosition = new Vector2(0, -index * rect.rect.height);

        // Activate the object
        pooledObject.SetActive(true);
    }

    // Call this when you want to hide a character (return to pool)
    public void <PERSON><PERSON><PERSON><PERSON>cter(GameObject pooledObject)
    {
        pooledObject.SetActive(false);
    }
}
