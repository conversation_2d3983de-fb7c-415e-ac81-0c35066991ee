using UnityEngine;
using System.Collections.Generic;

public class CharPartyPool : MonoBehaviour
{
    public GameObject characterPartyPrefab;
    public Transform parent;
    public int poolSize = 20; // Adjust based on visible area

    private List<GameObject> partyPool = new List<GameObject>();

    private void Awake()
    {
        for (int i = 0; i < poolSize; i++)
        {
            GameObject partyObj = Instantiate(characterPartyPrefab, parent);
            partyObj.name = "PartyChar " + i;
            partyObj.SetActive(false);
            partyPool.Add(partyObj);
        }
    }

    public GameObject GetPooledPartyObject()
    {
        foreach (var partyObj in partyPool)
        {
            if (!partyObj.activeInHierarchy)
                return partyObj;
        }
        return null; // Or expand pool if needed
    }

    public void ReturnPartyToPool(GameObject partyObj)
    {
        //CharacterValuesParty charParty = partyObj.GetComponent<CharacterValuesParty>();
        //charParty.ResetCharacter(); // Assuming you have a method to reset character state
        partyObj.SetActive(false);
    }
}
