using UnityEngine;
using UnityEngine.UI;

public class StickyUIElement : MonoBehaviour // Makes a copy of stock section stick to the top of the scroll
{
    public PartyConfigs partyConfigs;

    public RectTransform party0;
    public RectTransform party1;
    public RectTransform party2;
    public RectTransform party3;
    public RectTransform party4;
    RectTransform stickyOriginal;  // The original sticky element inside your grid
    public GameObject stickyCopy;          // The sticky copy that appears fixed at top
    public ScrollRect scrollRect;           // Your ScrollRect component
    public RectTransform scrollContent;    // The scroll content RectTransform
    public float stickThreshold = 5f;      // Threshold for sticking (tweak as needed)

    private bool isStuck = false;

    void Start()
    {
        stickyCopy.SetActive(false);
        SwitchStickyOriginal(partyConfigs.partyIndex);
    }

    void Update()
    {
        if (stickyOriginal == null || stickyCopy == null || scrollRect == null || scrollContent == null)
            return;

        // Calculate scroll info
        float viewportHeight = scrollRect.viewport.rect.height;
        float contentHeight = scrollContent.rect.height;
        float scrollY = (1 - scrollRect.verticalNormalizedPosition) * (contentHeight - viewportHeight);

        // Get stickyOriginal anchoredPosition.y relative to scrollContent (or its parent grid)
        float stickyPosY = stickyOriginal.anchoredPosition.y;

        // Distance from stickyOriginal to viewport top (viewport top is at scrollY = 0)
        float distanceToTop = stickyPosY + scrollY;

        // If stickyOriginal scrolls above the top (distanceToTop > threshold), show stickyCopy
        if (!isStuck && distanceToTop > stickThreshold)
        {
            stickyCopy.SetActive(true);
            stickyOriginal.GetComponent<CanvasGroup>().alpha = 0;
            isStuck = true;
        }
        // If stickyOriginal moves back into view (distanceToTop < threshold), hide stickyCopy
        else if (isStuck && distanceToTop < stickThreshold)
        {
            stickyCopy.SetActive(false);
            stickyOriginal.GetComponent<CanvasGroup>().alpha = 1;
            isStuck = false;
        }
    }

    public void SwitchStickyOriginal(int partyIndex)
    {
        switch (partyIndex)
        {
            case 0:
                stickyOriginal = party0.GetChild(2).GetComponent<RectTransform>();
                return;
            case 1:
                stickyOriginal = party1.GetChild(2).GetComponent<RectTransform>();
                return;
            case 2:
                stickyOriginal = party2.GetChild(2).GetComponent<RectTransform>();
                return;
            case 3:
                stickyOriginal = party3.GetChild(2).GetComponent<RectTransform>();
                return;
            case 4:
                stickyOriginal = party4.GetChild(2).GetComponent<RectTransform>();
                return;
            default:
                stickyOriginal = party0.GetChild(2).GetComponent<RectTransform>();
                return;
        }
    }
}
