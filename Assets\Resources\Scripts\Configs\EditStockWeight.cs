using TMPro;
using UnityEngine;

public class EditStockWeight : MonoBeh<PERSON>our
{
    private ConfigsHandler configsHandler; // Reference to the ConfigsHandler
    private TMP_InputField text;

    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script

        text = GetComponent<TMP_InputField>(); // Get the TMP_InputField component

        text.text = configsHandler.StockWeight.ToString(); // Add the listener to the TMP_InputField to edit the value

        text.onEndEdit.AddListener(EditValue); // Add the listener to the TMP_InputField to edit the value
    }

    void EditValue(string arg) // Edit the value of the stock weight
    {
        if (float.Parse(arg) < 0 || float.Parse(arg) > 1) arg = "0,5"; // If the value is less than 0 or greater than 1 set it to 0.5

        text.text = arg;

        configsHandler.StockWeight = float.Parse(arg); // Set the stock weight to the new value
        configsHandler.SaveValues(); // Save the value
    }
}
