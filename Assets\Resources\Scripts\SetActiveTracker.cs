using System;
using UnityEngine;

public class SetActiveTracker : MonoBehaviour
{
    private bool lastActive;

    void Awake()
    {
        lastActive = gameObject.activeSelf;
    }

    void Update()
    {
        if (lastActive != gameObject.activeSelf)
        {
            Debug.LogWarning($"{gameObject.name} .SetActive({gameObject.activeSelf}) called!\nStack Trace:\n{Environment.StackTrace}", this);
            lastActive = gameObject.activeSelf;
        }
    }

    void OnDisable()
    {
        Debug.LogWarning($"{gameObject.name} was DISABLED via SetActive(false)!\nStack Trace:\n{Environment.StackTrace}", this);
    }
}

