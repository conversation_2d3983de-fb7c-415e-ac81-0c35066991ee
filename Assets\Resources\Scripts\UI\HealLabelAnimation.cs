using DG.Tweening;
using UnityEngine;

public class HealLabelAnimation : MonoBehaviour
{
    private RectTransform rectTransform;
    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        rectTransform = GetComponent<RectTransform>();
        rectTransform.localScale = Vector3.zero;

        Animation();
    }

    // Update is called once per frame
    void Update()
    {

    }

    void Animation()
    {
        Vector2 startPos = rectTransform.anchoredPosition;

        Sequence scaleSeq = DOTween.Sequence();
        scaleSeq.Append(rectTransform.DOScale(1f * 1.3f, 0.5f).SetEase(Ease.OutBack));
        scaleSeq.Join(rectTransform.DOAnchorPos(startPos + new Vector2(15f, 15f), 1.5f).SetEase(Ease.OutSine));

        scaleSeq.Append(rectTransform.DOScale(0, 0.5f).SetEase(Ease.OutSine)).OnComplete(() =>
        {
            Destroy(gameObject);
        });
    }
}
