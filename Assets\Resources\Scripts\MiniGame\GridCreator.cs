using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public class GridCreator : MonoBehaviour
{
    public Camera mainCamera;

    private static Grid Grid;

    public GameObject energyTimeProgressBar, TileBackground;

    ConfigsHandler configsHandler; // configs handler script

    public int width, height; // grid size

    public Color FirstColor, SecondColor; // colors of the destroyed tiles effect

    public bool useDefaultColors = true;

    [Range(0.001f, 2f)]
    public float cellSize; // cell size

    public static float CellSize;

    public Vector3 tempPos;

    public List<TileDestroy> tilesToEffect = new(); // list of the tiles to effect

    // bools
    bool canUpdate = true;
    bool canRearange = true;
    public bool animationEnded = true;

    bool canMoveBackground = true;
    bool didTurnsSwitch = true;

    bool canPlayerPlay = true;

    public bool playerTurn = true;

    bool NotvalidPlayerSelected = true;

    void Start()
    {
        StartCoroutine(TileDestroyAnimationCoroutine()); // starts the tile destroy animation coroutine

        TileBackground = GameObject.Find("TileBackground");

        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script

        if (useDefaultColors) // change the colors if setted to use the default ones
        {
            FirstColor = new(0.3f, 0.5f, 1, 0.7f);
            SecondColor = new(1, 0.5f, 0.3f, 0.7f);
        }

        CellSize = cellSize; // set the cell size

        Grid = new Grid(CellSize, width, height, transform.position.x, transform.position.y, this, configsHandler); // create the grid

        StartCoroutine(Grid.ConstructTiles()); // construct the tiles

        canPlayerPlay = true;
    }

    void Update()
    {

        // move the background fowards and backwards depending who the turn is
        if ((!didTurnsSwitch && canMoveBackground)
        ||
        (canPlayerPlay && !configsHandler.IsThereAnyPlayerNotInCooldown())
        ||
        (!NotvalidPlayerSelected && configsHandler.GetPlayerCharacter(configsHandler.GetSelectedPlayer()) == null)
        || configsHandler.turns % 2 != 0)
        {
            TileBackground.transform.SetAsLastSibling();
            if (!didTurnsSwitch && canMoveBackground) canMoveBackground = false;
            if (canPlayerPlay && !configsHandler.IsThereAnyPlayerNotInCooldown()) canPlayerPlay = false;
            if (!NotvalidPlayerSelected && configsHandler.GetPlayerCharacter(configsHandler.GetSelectedPlayer()) == null) NotvalidPlayerSelected = true;
        }

        if (((didTurnsSwitch && !canMoveBackground)
            ||
            (!canPlayerPlay && configsHandler.IsThereAnyPlayerNotInCooldown())
            ||
            (NotvalidPlayerSelected && configsHandler.GetPlayerCharacter(configsHandler.GetSelectedPlayer()) != null)
            || configsHandler.turns % 2 == 0)
            &&
            configsHandler.IsPPLeft()
            &&
            configsHandler.GetPlayerCharacter(configsHandler.GetSelectedPlayer()) != null
            )
        {
            TileBackground.transform.SetAsFirstSibling();
            if (didTurnsSwitch && !canMoveBackground) canMoveBackground = true;
            if (!canPlayerPlay && configsHandler.IsThereAnyPlayerNotInCooldown()) canPlayerPlay = true;
            if (NotvalidPlayerSelected && configsHandler.GetPlayerCharacter(configsHandler.GetSelectedPlayer()) != null) NotvalidPlayerSelected = false;
        }

        // start the coroutines for the grid
        StartCoroutine(Grid.MatchedTiles());
        StartCoroutine(Grid.FixTiles());
        StartCoroutine(UpdateTiles());
        StartCoroutine(TilesAnimator());

        // check if the player can drag a tile
        if (Input.touchCount > 0
            &&
            Input.GetTouch(0).phase == TouchPhase.Began
            &&
            animationEnded
            &&
            mainCamera.enabled
            &&
            playerTurn
            &&
            GameObject.Find("ActionMenu") == null
            &&
            configsHandler.IsThereAnyPlayerNotInCooldown()
            &&
            configsHandler.GetPlayerCharacter(configsHandler.GetSelectedPlayer()) != null
            &&
            !configsHandler.CantPlayerPlay())
        {
            StartCoroutine(DragTile());
        }
    }

    //public static void ResetScore() => Grid.score.points = 0; // reset the score

    public void DoTileDestroyAnimation(TileDestroy tile) => tilesToEffect.Add(tile); // add the tile to the list of the tiles to effect

    private IEnumerator DragTile() // drag the tile
    {
        if (Grid.TryGetTileUnderCursor(out int x, out int y)) // get the tile under the cursor
        {
            // if there are no characters, reset the energy time
            if (!configsHandler.IsThereCharacters()) configsHandler.SetEnergyTime();

            //ResetScore();

            // reset the current combo
            configsHandler.currentCombo = -1;
            // reset the combo
            //Grid.score.combo = -1;

            // makes the grid not able to rearrange
            canRearange = false;
            // gets the position of the tile
            tempPos = Grid.tiles[x, y].tile.transform.position;
            // makes the tile being dragged
            Grid.tiles[x, y].isDragged = true;
            // saves the position of the tile image
            var tempPos2 = Grid.tiles[x, y].tileImg.transform.position;
            // moves the tile image 3 units in the z axis
            Grid.tiles[x, y].tileImg.transform.position = new Vector3(tempPos2.x, tempPos2.y, -3f);

            // makes the grid not able to update
            canUpdate = false;

            // while the player is dragging the tile and there is energy
            while ((Input.GetTouch(0).phase == TouchPhase.Began || Input.GetTouch(0).phase == TouchPhase.Stationary || Input.GetTouch(0).phase == TouchPhase.Moved) && configsHandler.IsEnergyLeft())
            {
                Grid.MoveTile(x, y);
                yield return null;
            }
            // resets the position of the tile and the tile's image, makes the tile not being dragged and switches the turn
            tempPos2 = Grid.tiles[x, y].tileImg.transform.position;
            Grid.tiles[x, y].tileImg.transform.position = new Vector3(tempPos2.x, tempPos2.y, 0);
            Grid.tiles[x, y].tile.transform.position = tempPos;
            Grid.tiles[x, y].isDragged = false;

            if(!configsHandler.canCountdown) yield break;

            StartCoroutine(SwitchTurns());
        }
    }

    private IEnumerator TilesAnimator() // handles the animation of the tiles
    {
        animationEnded = true;
        if (canUpdate)
        {
            for (int w = 0; w < width; w++)
            {
                float baseSpeed = CellSize / 8f * (Time.deltaTime * 60f); // set the base speed

                for (int h = 0; h < height; h++)
                {
                    var endPos = new Vector3(Grid.tiles[w, h].tile.transform.position.x, -(h * CellSize - transform.position.y), transform.position.z); // gets the position that the tile will end up
                    if (Grid.tiles[w, h].tile.transform.position != endPos || (Grid.tiles[w, h].tile.transform.position == endPos && !Grid.tiles[w, h].tile.activeSelf))
                    {
                        StartCoroutine(Grid.tiles[w, h].UpdateTile(endPos, baseSpeed));
                        baseSpeed += Mathf.Pow(1.1f, Time.deltaTime * 60f) * Time.deltaTime;
                    }
                    if (!(animationEnded && Grid.tiles[w, h].tile.transform.position == endPos)) animationEnded = false;
                }
            }
        }
        if (animationEnded) Grid.runeCheck = true;
        yield return null;
    }

    public IEnumerator SwitchTurns() // switches the turns
    {
        configsHandler.canCountdown = false;
        canRearange = true;
        if (configsHandler.IsThereCharacters() && configsHandler.IsPPLeft()) // executes if there are characters, being active players and enemies, and if there is any player not in cooldown
        {
            didTurnsSwitch = false;
            playerTurn = false;

            bool enemyTurn = false;
            while (!enemyTurn) // waits for the enemy turn
            {
                if (animationEnded) enemyTurn = true;
                if (enemyTurn)
                    // needed to wait 5 frames to make it consistante, because theres always a frame that the tile falling animation isn't finished but the animationEnded is true
                    for (int i = 0; i < 5 && enemyTurn; i++)
                    {
                        yield return null;
                        if (!animationEnded) enemyTurn = false;
                    }
                yield return new WaitForSeconds(0);
            }

            if (configsHandler.CantPlayerPlay() || !configsHandler.IsThereAnyPlayerNotInCooldown())
            {
                if (configsHandler.playerEmptyActions == 0) yield return new WaitForSeconds(0.5f);
                configsHandler.lostActions = configsHandler.playerEmptyActions;
            }
            configsHandler.playerActions--; // resets the player actions
            configsHandler.playerEmptyActions++;

            // checks if the player did crit, if not, resets the cooldown
            PlayerInterface player = configsHandler.playersInterface[configsHandler.GetSelectedPlayer()].GetComponent<PlayerInterface>();

            if (!configsHandler.CantPlayerPlay() && configsHandler.IsThereAnyPlayerNotInCooldown())
                if (player.didCrit) player.didCrit = false;
                else if (player.AttackCooldown.fillAmount <= 0.0001f) player.AttackCooldown.fillAmount = 1;

            if (configsHandler.playerActions > 0)
            {
                playerTurn = true;
                didTurnsSwitch = true;
                if (configsHandler.CantPlayerPlay() || !configsHandler.IsThereAnyPlayerNotInCooldown())
                {
                    yield return new WaitForSeconds(1f);
                    StartCoroutine(SwitchTurns());
                }
                yield break;
            }
            yield return null;
            configsHandler.playerActions = 1;
            configsHandler.playerEmptyActions = 0;

            GameObject.Find("SwitchTurnsEffect").GetComponent<SwitchTurnsAnimation>().SwitchTurns("Enemy Turn", Color.red); // starts the switch turns animation


            configsHandler.playerTurns++;
            configsHandler.turns++;

            configsHandler.lostActions = -1;

            // starts the enemy turn
            //yield return StartCoroutine(configsHandler.EnemyAttack(Grid.score.points));

            configsHandler.lostActions = -1;

            if (!configsHandler.CantPlayerPlay() && configsHandler.IsThereAnyPlayerNotInCooldown()) GameObject.Find("SwitchTurnsEffect").GetComponent<SwitchTurnsAnimation>().SwitchTurns("Player Turn", Color.cyan); // starts the switch turns animation

                configsHandler.enemyTurns++;
                configsHandler.turns++;

            didTurnsSwitch = true;
            playerTurn = true;
            configsHandler.enemyActions = 1;
            configsHandler.enemyEmptyActions = 0;

            if (configsHandler.CantPlayerPlay() || !configsHandler.IsThereAnyPlayerNotInCooldown()) StartCoroutine(SwitchTurns());
        }
    }

    public IEnumerator TileDestroyAnimationCoroutine() // handles the tile destroy animation
    {
        while (true)
        {
            if(tilesToEffect.Count > 0)
            {
                tilesToEffect.RemoveAll(t => t.circleEffect == null); // removes the tiles effects that are already destroyed
                foreach (var tile in tilesToEffect.FindAll(t => !t.isAnimating)) // executes for each tile effect that isn't animating
                {
                    StartCoroutine(tile.Effect());
                }
            }
            yield return new WaitForSeconds(0);
        }
    }

    public IEnumerator UpdateTiles() // rearanges the tiles positions inside the array
    {
        if (canRearange)
        {
            bool tilesSwitched;
            do
            {
                tilesSwitched = false;
                for (int w = 0; w < width; w++)
                {
                    for (int h = 0; h < height; h++)
                    {
                        if (Grid.tileIDS[w, h] != Grid.tiles[w, h].id)
                        {
                            int[] tileIndex = Tools.FindIndexes(Grid.tiles, Grid.tileIDS[w, h]); // gets the indexes of the tile

                            (Grid.tiles[w, h], Grid.tiles[tileIndex[0], tileIndex[1]]) = (Grid.tiles[tileIndex[0], tileIndex[1]], Grid.tiles[w, h]); // switches the tiles

                            tilesSwitched = true;
                        }
                    }
                }
            } while (tilesSwitched);
            canUpdate = true;
        }
        //Grid.score.UpdateText();
        yield return null;
    }

    public void UpdateTempTiles(GameObject collidedOBJ, GameObject originalOBJ) // switches the tiles possition on the id array
    {
        // starts the countdown
        //StartCoroutine(configsHandler.CountDown());

        // play the rune switch sound effect
        //AudioSource soundEffects = configsHandler.transform.GetComponent<AudioSource>();
        //AudioClip runeSwitchClip = Resources.Load<AudioClip>("SFX/RuneSwitch");
        //soundEffects.PlayOneShot(runeSwitchClip, configsHandler.sfxVolume.value * configsHandler.mainVolume.value);

        // gets their indexes
        var iCollided = Tools.FindIndexes(Grid.tiles, collidedOBJ);
        var iCurrent = Tools.FindIndexes(Grid.tiles, originalOBJ);

        // gets the indexes inside the array of the ids with the tiles ids
        iCollided = Tools.FindIndexes(Grid.tileIDS, Grid.tiles[iCollided[0], iCollided[1]].id);
        iCurrent = Tools.FindIndexes(Grid.tileIDS, Grid.tiles[iCurrent[0], iCurrent[1]].id);

        // switches the tiles
        (Grid.tileIDS[iCurrent[0], iCurrent[1]], Grid.tileIDS[iCollided[0], iCollided[1]]) = (Grid.tileIDS[iCollided[0], iCollided[1]], Grid.tileIDS[iCurrent[0], iCurrent[1]]);
    }

    // gets the indexes of a tile, this exists because this way i didn't need to make the grid array public
    public static int[] GetIndexes(GameObject tile) => Tools.FindIndexes(Grid.tiles, tile);

    // checks if a tile is being dragged
    public static bool IsDragged(int[] indexes) => Grid.tiles[indexes[0], indexes[1]].isDragged;

    public static Score GetScoreBoard() => Grid.score;
}

public static class Tools
{
    public static float NormalizeValue(float min, float max, float current) => Mathf.Max(0, Mathf.Min(1, (current - min) / (max - min)));

    public static Vector3 ExponentialInverseLerp(Vector3 start, Vector3 end, float t)
    {
        t = Mathf.Clamp01(t);
        t = 1 - Mathf.Pow(1 - t, 2); // Interpola��o exponencial inversa
        return Vector3.Lerp(start, end, t);
    }
    public static float Sign(float num) => num > 0 || num < -0 ? Mathf.Sign(num) : 0;

    public static Color FromArgb(int red, int green, int blue, int alpha = 255)
    {
        return new Color(red / 255f, green / 255f, blue / 255f, alpha / 255f);
    }


    public static Color InterpolateColor(Color startColor, Color endColor, float position, float offset = 0)
    {
        // Ajusta a posi��o com base no offset
        position = Mathf.Clamp01(position + offset / 100);

        // Interpola entre as cores
        float r = Mathf.Lerp(endColor.r, startColor.r, position);
        float g = Mathf.Lerp(endColor.g, startColor.g, position);
        float b = Mathf.Lerp(endColor.b, startColor.b, position);
        float a = Mathf.Lerp(endColor.a, startColor.a, position);

        return new Color(r, g, b, a);
    }

    // does the exact same thing that Vector3.MoveTowards do but for colors
    public static Color MoveTowardsColor(Color current, Color target, float maxDistanceDelta)
    {
        float num = target.r - current.r;
        float num2 = target.g - current.g;
        float num3 = target.b - current.b;
        float num4 = target.a - current.a;
        float num5 = num * num + num2 * num2 + num3 * num3 + num4 * num4;
        if (num5 <= 0f || (maxDistanceDelta >= 0f && num5 <= maxDistanceDelta * maxDistanceDelta))
        {
            return target;
        }

        float num6 = Mathf.Sqrt(num5);
        return new Color(current.r + num / num6 * maxDistanceDelta, current.b + num2 / num6 * maxDistanceDelta, current.b + num3 / num6 * maxDistanceDelta, current.a + num4 / num6 * maxDistanceDelta);
    }

    // gets the number of tiles that are going to be destroyed and and an array of the indexes order that they need to go
    public static void NumOfDestroyedTiles(Tile[,] tiles, int w, out int num, out int[] notDestroyed)
    {
        num = 0;
        notDestroyed = new int[tiles.GetLength(1)];
        for (int h = 0; h < tiles.GetLength(1); h++)
        {
            if (tiles[w, h].isDestroyed)
            {
                num++;
                notDestroyed[h] = -1;
            }
            else notDestroyed[h] = h;
        }
    }

    // gets the indexes of the first destroyed tile, the code is from here https://stackoverflow.com/questions/3260935/finding-position-of-an-element-in-a-two-dimensional-array
    public static int[] FindIndexes(this Tile[,] haystack, object needdle)
    {
        if (haystack.Rank == 1)
            return new[] { Array.IndexOf(haystack, needdle) };

        Func<Tile, bool> predicate;
        if (needdle.GetType().ToString() == "UnityEngine.GameObject")
        {
            predicate = x => x.tile.Equals(needdle);
        }
        else if (needdle.GetType().ToString() == "System.Boolean")
        {
            predicate = x => x.isDestroyed.Equals(needdle);
        }
        else
        {
            predicate = x => x.id.Equals(needdle);
        }

        var found = haystack.OfType<Tile>()
            .Select((value, index) => new { value, index })
            .FirstOrDefault(x => predicate(x.value));

        if (found == null)
        {
            return new[] { -1, -1 };
        }

        var indexes = new int[haystack.Rank];
        var last = found.index;
        var lastLegth = Enumerable.Range(0, haystack.Rank)
            .Aggregate(1, (acc, value) => acc * haystack.GetLength(value));
        for (var rank = 0; rank < haystack.Rank; rank++)
        {
            lastLegth /= haystack.GetLength(rank);
            var value = last / lastLegth;
            last -= value * lastLegth;

            var index = value + haystack.GetLowerBound(rank);
            indexes[rank] = index;
        }
        return indexes;
    }

    // gets the indexes of the first destroyed tile using the ids instead of the tile object
    public static int[] FindIndexes(this int[,] haystack, object needdle)
    {
        if (haystack.Rank == 1)
            return new[] { Array.IndexOf(haystack, needdle) };
        var found = haystack.OfType<int>()
            .Select((value, index) => new { value, index })
            .FirstOrDefault(x => x.value.Equals(needdle));


        if (found == null)
        {
            Debug.LogError("ID not found in array");
            return new[] { -1, -1 };
        }

        var indexes = new int[haystack.Rank];
        var last = found.index;
        var lastLegth = Enumerable.Range(0, haystack.Rank)
            .Aggregate(1, (acc, value) => acc * haystack.GetLength(value));
        for (var rank = 0; rank < haystack.Rank; rank++)
        {
            lastLegth /= haystack.GetLength(rank);
            var value = last / lastLegth;
            last -= value * lastLegth;

            var index = value + haystack.GetLowerBound(rank);
            indexes[rank] = index;
        }
        return indexes;
    }
}