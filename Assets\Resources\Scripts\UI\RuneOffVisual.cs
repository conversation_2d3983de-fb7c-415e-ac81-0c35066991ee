using System;
using Unity.VisualScripting;
using UnityEngine;
using UnityEngine.UI;

public class RuneOffVisual : MonoBehaviour
{
    public static RuneOffVisual Instance;
    public RectTransform dragVisual;
    public Image dragImage;
    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        dragVisual = gameObject.GetComponent<RectTransform>();
        dragImage = gameObject.GetComponent<Image>();
        Instance = this;
        Hide();
    }

    // Update is called once per frame
    void Update()
    {

    }

    public void Hide()
    {
        gameObject.SetActive(false);
    }

    public void Show(Types type)
    {
        dragImage.sprite = Resources.Load<Sprite>($"Sprites/UI/{type}");
        gameObject.SetActive(true);
    }

    public void UpdatePosition(Vector2 worldPosition)
    {
        dragVisual.position = worldPosition;
    }
}
