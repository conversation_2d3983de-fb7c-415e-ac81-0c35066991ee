using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// Tracks character modifications to enable incremental saving of only affected chunks
/// This dramatically improves save performance by avoiding full dataset serialization
/// </summary>
public class CharacterChangeTracker
{
    private HashSet<string> dirtyCharacterIds = new HashSet<string>();
    private Dictionary<string, int> characterToIndex = new Dictionary<string, int>();
    private bool isFullSaveRequired = false;

    /// <summary>
    /// Singleton instance for global access
    /// </summary>
    public static CharacterChangeTracker Instance { get; private set; }

    static CharacterChangeTracker()
    {
        Instance = new CharacterChangeTracker();
    }

    /// <summary>
    /// Updates the character index mapping when the character list changes
    /// </summary>
    public void UpdateCharacterMapping(List<BattleCharacter> characters)
    {
        characterToIndex.Clear();

        for (int i = 0; i < characters.Count; i++)
        {
            if (characters[i] != null && !string.IsNullOrEmpty(characters[i].id))
            {
                characterToIndex[characters[i].id] = i;
            }
        }

        //Debug.Log($"[CHANGE_TRACKER] 📋 Updated mapping for {characterToIndex.Count} characters");
    }

    /// <summary>
    /// Marks a character as modified (dirty) for incremental saving
    /// </summary>
    public void MarkCharacterDirty(string characterId)
    {
        if (string.IsNullOrEmpty(characterId))
        {
            //Debug.LogWarning("[CHANGE_TRACKER] ⚠️ Attempted to mark null/empty character ID as dirty");
            return;
        }

        dirtyCharacterIds.Add(characterId);
        //Debug.Log($"[CHANGE_TRACKER] 🔄 Marked character {characterId} as dirty");
    }

    /// <summary>
    /// Marks a character as modified by index
    /// </summary>
    public void MarkCharacterDirtyByIndex(int characterIndex, List<BattleCharacter> characters)
    {
        if (characterIndex >= 0 && characterIndex < characters.Count && characters[characterIndex] != null)
        {
            MarkCharacterDirty(characters[characterIndex].id);
        }
    }

    /// <summary>
    /// Marks multiple characters as dirty
    /// </summary>
    public void MarkCharactersDirty(IEnumerable<string> characterIds)
    {
        foreach (string id in characterIds)
        {
            MarkCharacterDirty(id);
        }
    }

    /// <summary>
    /// Forces a full save on the next save operation (used for structural changes)
    /// </summary>
    public void RequireFullSave()
    {
        isFullSaveRequired = true;
        //Debug.Log("[CHANGE_TRACKER] 🔄 Full save required for next operation");
    }

    /// <summary>
    /// Saves only the chunks containing dirty characters
    /// </summary>
    public void SaveDirtyCharacters(List<BattleCharacter> allCharacters)
    {
        try
        {
            if (isFullSaveRequired /*|| dirtyCharacterIds.Count == 0*/)
            {
                SaveAllCharacters(allCharacters);
                return;
            }

            // Get affected chunk indices
            var affectedChunks = GetAffectedChunks(allCharacters);

            if (affectedChunks.Count == 0)
            {
                //Debug.Log("[CHANGE_TRACKER] ℹ️ No chunks affected, skipping save");
                return;
            }

            //Debug.Log($"[CHANGE_TRACKER] 💾 Saving {affectedChunks.Count} affected chunks for {dirtyCharacterIds.Count} dirty characters");

            // Save each affected chunk
            foreach (int chunkIndex in affectedChunks)
            {
                JsonSaveHelper.SaveCharacterChunk(allCharacters, chunkIndex);
            }

            // Clear dirty tracking
            ClearDirtyTracking();

            //Debug.Log($"[CHANGE_TRACKER] ✅ Incremental save completed for chunks: {string.Join(", ", affectedChunks)}");
        }
        catch (Exception ex)
        {
            Debug.LogError($"[CHANGE_TRACKER] ❌ Failed to save dirty characters: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// Saves all characters to chunked storage (full save)
    /// </summary>
    public void SaveAllCharacters(List<BattleCharacter> allCharacters)
    {
        try
        {
            //Debug.Log($"[CHANGE_TRACKER] 💾 Performing full save of {allCharacters.Count} characters");

            int totalChunks = (int)Math.Ceiling((double)allCharacters.Count / JsonSaveHelper.CHARACTERS_PER_CHUNK);

            // Save all chunks
            for (int chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++)
            {
                JsonSaveHelper.SaveCharacterChunk(allCharacters, chunkIndex);
            }

            // Clean up any extra chunk files that might exist
            CleanupExtraChunkFiles(totalChunks);

            // Clear dirty tracking
            ClearDirtyTracking();

            //Debug.Log($"[CHANGE_TRACKER] ✅ Full save completed: {totalChunks} chunks");
        }
        catch (Exception ex)
        {
            Debug.LogError($"[CHANGE_TRACKER] ❌ Failed to save all characters: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// Gets the chunk indices affected by dirty characters
    /// </summary>
    private HashSet<int> GetAffectedChunks(List<BattleCharacter> allCharacters)
    {
        var affectedChunks = new HashSet<int>();

        foreach (string dirtyId in dirtyCharacterIds)
        {
            if (characterToIndex.TryGetValue(dirtyId, out int characterIndex))
            {
                int chunkIndex = JsonSaveHelper.GetChunkIndexForCharacter(characterIndex);
                affectedChunks.Add(chunkIndex);
            }
        }

        return affectedChunks;
    }

    /// <summary>
    /// Clears all dirty tracking state
    /// </summary>
    private void ClearDirtyTracking()
    {
        dirtyCharacterIds.Clear();
        isFullSaveRequired = false;
    }

    /// <summary>
    /// Removes chunk files that are no longer needed
    /// </summary>
    private void CleanupExtraChunkFiles(int validChunkCount)
    {
        int chunkIndex = validChunkCount;
        while (JsonSaveHelper.FileExists($"characters_chunk_{chunkIndex}.json"))
        {
            string fileName = $"characters_chunk_{chunkIndex}.json";
            string filePath = System.IO.Path.Combine(JsonSaveHelper.GetSaveFolder(), fileName);
            System.IO.File.Delete(filePath);
            //Debug.Log($"[CHANGE_TRACKER] 🗑️ Cleaned up extra chunk file: {fileName}");
            chunkIndex++;
        }
    }

    /// <summary>
    /// Gets current dirty character count for debugging
    /// </summary>
    public int GetDirtyCharacterCount()
    {
        return dirtyCharacterIds.Count;
    }

    /// <summary>
    /// Gets whether a full save is required
    /// </summary>
    public bool IsFullSaveRequired()
    {
        return isFullSaveRequired;
    }
}
