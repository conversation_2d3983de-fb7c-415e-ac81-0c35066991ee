using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class CharResContainer : MonoBehaviour
{
    public ConfigsHandler configsHandler;
    public PartyConfigs partyConfigs;

    // // List of the gameobjects of the chars
    readonly List<GameObject> resChars = new();

    // Sprite cache for Types
    private readonly Dictionary<Types, Sprite> typeSpriteCache = new();

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        // Gets the gameObject of each char
        for (int i = 0; i < 4; i++) resChars.Add(transform.GetChild(i).gameObject);

        LoadAllTypeSprites(); //Preloads sprites

        RefreshCharResUI();
    }

    // Update is called once per frame
    void Update()
    {


    }

    public void RefreshCharResUI() // Refreshes the char with up to date info
    {
        PartyCharacters partyCharacters = configsHandler.GetPartyCharacters(partyConfigs.partyIndex);

        foreach (GameObject resChar in resChars)
        {
            BattleCharacter character = partyCharacters.activeCharacters[resChar.transform.GetSiblingIndex()];

            if (character != null) // if the character isn't null, it show the values
            {
                if (character.IsDead) resChar.SetActive(true); else resChar.SetActive(false);

                resChar.transform.GetChild(0).gameObject.SetActive(false);
                resChar.transform.GetChild(1).gameObject.SetActive(true);
                resChar.transform.GetChild(2).gameObject.SetActive(true);
                resChar.transform.GetChild(3).gameObject.SetActive(true);
                resChar.transform.GetChild(4).gameObject.SetActive(true);
                resChar.transform.GetChild(6).gameObject.SetActive(true);
                resChar.transform.GetChild(7).gameObject.SetActive(true);
                resChar.transform.GetChild(8).gameObject.SetActive(true);




                resChar.transform.GetChild(2).GetComponent<TextMeshProUGUI>().text = character.name;
                resChar.transform.GetChild(3).GetComponent<TextMeshProUGUI>().text = "Lv: " + character.level.ToString();
                resChar.transform.GetChild(4).GetChild(0).GetComponent<Image>().fillAmount = (character.maxHP == 0) ? 1 : (float)character.hP / character.maxHP;
                resChar.transform.GetChild(4).GetChild(1).GetChild(0).GetComponent<TextMeshProUGUI>().text = character.hP.ToString();
                Types type1 = character.skills.GetHighestSpDef();
                resChar.transform.GetChild(6).GetChild(0).GetComponent<Image>().sprite = GetTypeSprite(type1);
                Types type2 = character.skills.GetHighestSpAtk();
                resChar.transform.GetChild(7).GetChild(0).GetComponent<Image>().sprite = GetTypeSprite(type2);
                Types type3 = character.skills.GetLowestSpDef();
                resChar.transform.GetChild(8).GetChild(0).GetComponent<Image>().sprite = GetTypeSprite(type3);

                if (character.IsDead)
                {
                    resChar.transform.GetChild(9).gameObject.SetActive(true);
                    resChar.transform.GetChild(10).gameObject.SetActive(true);
                    resChar.transform.GetChild(9).GetComponent<Image>().color = new Color(1f, 0f, 0f, 0.4f);
                    resChar.transform.GetChild(10).GetComponent<TextMeshProUGUI>().text = "DEAD";

                }
                else
                {
                    resChar.transform.GetChild(9).gameObject.SetActive(false);
                    resChar.transform.GetChild(10).gameObject.SetActive(false);
                }

            }
            else // otherwise it hide the values
            {
                resChar.GetComponent<Button>().interactable = false;
                resChar.transform.GetChild(0).gameObject.SetActive(true);
                resChar.transform.GetChild(1).gameObject.SetActive(false);
                resChar.transform.GetChild(2).gameObject.SetActive(false);
                resChar.transform.GetChild(3).gameObject.SetActive(false);
                resChar.transform.GetChild(4).gameObject.SetActive(false);
                resChar.transform.GetChild(6).gameObject.SetActive(false);
                resChar.transform.GetChild(7).gameObject.SetActive(false);
                resChar.transform.GetChild(8).gameObject.SetActive(false);
                resChar.transform.GetChild(9).gameObject.SetActive(false);
                resChar.transform.GetChild(10).gameObject.SetActive(false);
            }
        }
    }

        void LoadAllTypeSprites()
    {
        foreach (Types type in System.Enum.GetValues(typeof(Types)))
        {
            if (!typeSpriteCache.ContainsKey(type))
            {
                Sprite sprite = Resources.Load<Sprite>($"Sprites/BattleEffects/{type}");
                if (sprite != null)
                {
                    typeSpriteCache[type] = sprite;
                }
                else
                {
                    Debug.LogWarning($"Missing sprite for type: {type}");
                }
            }
        }
    }
        Sprite GetTypeSprite(Types type)
    {
        if (typeSpriteCache.TryGetValue(type, out Sprite sprite))
        {
            return sprite;
        }
        else
        {
            Debug.LogWarning($"Sprite not found for type: {type}");
            return null;
        }
    }
}
