

public class CharacterAilementDefenses
{
    string charm, confusion, curse, paralysis, sleep;

    // Default constructor initializes ailment defenses to "0"
    public CharacterAilementDefenses()
    {
        charm = "";
        confusion = "";
        curse = "";
        paralysis = "";
        sleep = "";
    }

    // Individual getters for each ailment defense
    public string GetCharm() => charm;
    public string GetConfusion() => confusion;
    public string GetCurse() => curse;
    public string GetParalysis() => paralysis;
    public string GetSleep() => sleep;

    // Individual setters for each ailment defense
    public void SetCharm(string charm) => this.charm = charm;
    public void SetConfusion(string confusion) => this.confusion = confusion;
    public void SetCurse(string curse) => this.curse = curse;
    public void SetParalysis(string paralysis) => this.paralysis = paralysis;
    public void SetSleep(string sleep) => this.sleep = sleep;
}
