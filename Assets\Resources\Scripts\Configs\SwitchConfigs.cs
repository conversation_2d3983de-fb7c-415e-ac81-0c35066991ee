using UnityEngine;
using UnityEngine.UI;

public class SwitchConfigs : MonoBehaviour
{
    public GameObject CharArea;
    public GameObject AIArea;
    public GameObject PartyArea;
    public GameObject ResurrectArea;

    GameObject CharButton;
    GameObject AIButton;
    GameObject PartyButton;
    GameObject ResurrectButton;

    GameObject CharConfigs;
    GameObject AIConfigs;
    GameObject PartyConfigs;
    GameObject ResurrectConfigs;

    public GameObject GamePlayConfigs;

    public PartyConfigs partyConfigs;
    public CharResContainer charResContainer;

    void Start()
    {
        CharButton = CharArea.transform.GetChild(0).gameObject;
        AIButton = AIArea.transform.GetChild(0).gameObject;
        PartyButton = PartyArea.transform.GetChild(0).gameObject;
        ResurrectButton = ResurrectArea.transform.GetChild(0).gameObject;

        CharConfigs = CharArea.transform.GetChild(1).gameObject;
        AIConfigs = AIArea.transform.GetChild(1).gameObject;
        PartyConfigs = PartyArea.transform.GetChild(1).gameObject;
        ResurrectConfigs = ResurrectArea.transform.GetChild(1).gameObject;

        CharButton.GetComponent<Button>().onClick.AddListener(() => CharHandler());
        AIButton.GetComponent<Button>().onClick.AddListener(() => AIHandler());
        PartyButton.GetComponent<Button>().onClick.AddListener(() => PartyHandler());
        ResurrectButton.GetComponent<Button>().onClick.AddListener(() => ResurrectHandler());
    }

    private void CharHandler()
    {
        bool isActive = CharConfigs.activeSelf;

        if (isActive)
        {
            CharConfigs.SetActive(false);
            AIConfigs.SetActive(false);
            PartyConfigs.SetActive(false);
            ResurrectConfigs.SetActive(false);
            GamePlayConfigs.SetActive(true);
        }
        else
        {
            CharConfigs.SetActive(true);
            AIConfigs.SetActive(false);
            PartyConfigs.SetActive(false);
            ResurrectConfigs.SetActive(false);
            GamePlayConfigs.SetActive(false);
        }
    }

    private void AIHandler()
    {
        bool isActive = AIConfigs.activeSelf;

        if (isActive)
        {
            CharConfigs.SetActive(false);
            AIConfigs.SetActive(false);
            PartyConfigs.SetActive(false);
            ResurrectConfigs.SetActive(false);
            GamePlayConfigs.SetActive(true);
        }
        else
        {
            CharConfigs.SetActive(false);
            AIConfigs.SetActive(true);
            PartyConfigs.SetActive(false);
            ResurrectConfigs.SetActive(false);
            GamePlayConfigs.SetActive(false);
        }
    }

    private void PartyHandler()
    {
        bool isActive = PartyConfigs.activeSelf;

        if (isActive)
        {
            CharConfigs.SetActive(false);
            AIConfigs.SetActive(false);
            PartyConfigs.SetActive(false);
            ResurrectConfigs.SetActive(false);
            GamePlayConfigs.SetActive(true);
        }
        else
        {
            CharConfigs.SetActive(false);
            AIConfigs.SetActive(false);
            PartyConfigs.SetActive(true);
            ResurrectConfigs.SetActive(false);
            GamePlayConfigs.SetActive(false);
            // Refreshes the UI of the party
            partyConfigs.RefreshPartyUI();
        }


    }

    private void ResurrectHandler()
    {
        bool isActive = ResurrectConfigs.activeSelf;

        if (isActive)
        {
            CharConfigs.SetActive(false);
            AIConfigs.SetActive(false);
            PartyConfigs.SetActive(false);
            ResurrectConfigs.SetActive(false);
            GamePlayConfigs.SetActive(true);
        }
        else
        {
            CharConfigs.SetActive(false);
            AIConfigs.SetActive(false);
            PartyConfigs.SetActive(false);
            ResurrectConfigs.SetActive(true);
            GamePlayConfigs.SetActive(false);
            // Refreshes the UI of the party
            charResContainer.RefreshCharResUI();
        }
    }
}
