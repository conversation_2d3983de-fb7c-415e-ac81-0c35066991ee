using System.Collections.Generic;
using System.Linq;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class StickyOverlayForStock : MonoBehaviour  // Refreshes stock button sticky top overlay information and handles behaviour

{
    public ConfigsHandler configsHandler;
    public PartyConfigs partyConfigs;

    // // List of the gameobjects of the chars
    readonly List<GameObject> stickyChars = new();

    // Sprite cache for Types
    private readonly Dictionary<Types, Sprite> typeSpriteCache = new();

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        for (int i = 0; i < transform.childCount; i++)
        {
            GameObject stickyChar = transform.GetChild(i).gameObject;
            stickyChars.Add(stickyChar);
        }

        foreach (GameObject stickyChar in stickyChars)
        {
            var allButtons = stickyChar.GetComponentsInChildren<Button>();
            foreach (var slot in allButtons)
            {

                // Clear existing listeners to avoid duplicates
                slot.onClick.RemoveAllListeners();

                if (slot.name != "remChar") // if the button's name isn't "remChar" it gives the listener for it for selecting the slots of the party
                {
                    slot.onClick.AddListener(delegate {ButtonPassthrough(slot); });
                    var sbImage = slot.GetComponentsInChildren<Image>().FirstOrDefault(it => it.name == "SB");
                    if (sbImage != null)
                    {
                        sbImage.enabled = false;
                    }
                }
            }
        }

        LoadAllTypeSprites(); //Preloads sprites

        RefreshStickyPartyUI();
    }

    void ButtonPassthrough(Button button)  // whatever the do in the overlay happens in stock Buttons for the party
    {
        // Get index of the clicked sticky button
        int index = button.transform.GetSiblingIndex();

        foreach (var item in transform.GetComponentsInChildren<Button>().Where(b => b.name != "remChar")) // disables all the other selected buttons overlay
        {
            var sb1Image = item.GetComponentsInChildren<Image>().FirstOrDefault(it => it.name == "SB");
            if (sb1Image != null)
            {
                sb1Image.enabled = false;
            }
        }

        var sbImage = button.GetComponentsInChildren<Image>().FirstOrDefault(it => it.name == "SB");
        if (sbImage != null) sbImage.enabled = true;

        // Get matching PartyConfigs stock button for the same index
        GameObject partyGO = partyConfigs.transform.GetChild(partyConfigs.partyIndex).gameObject;

        Transform stockParent = partyGO.transform.GetChild(2).GetChild(1); // child(2) is stock section, child(1) is buttons holder
        Button matchingButton = stockParent.GetComponentsInChildren<Button>().Where(b => b.name != "remChar").ElementAtOrDefault(index);

        if (matchingButton != null)
        {
            partyConfigs.StartCoroutine(partyConfigs.SlotSelected(matchingButton));
        }
    }


    void OnEnable()
    {
        RefreshStickyPartyUI();
    }

    // Update is called once per frame
    void Update()
    {

    }

    public void RefreshStickyPartyUI()
    {
        PartyCharacters partyCharacters = configsHandler.GetPartyCharacters(partyConfigs.partyIndex);

        foreach (GameObject stickyChar in stickyChars)
        {
            // gets the character
            BattleCharacter character = partyCharacters.stockCharacters[stickyChar.transform.GetSiblingIndex()];

            if (character != null) // if the character isn't null, it show the values
            {
                bool isCharNotActive = partyCharacters.activeCharacters.FirstOrDefault(c => c == character) == null;
                bool isCharDead = character.IsDead;

                stickyChar.GetComponent<Button>().interactable = isCharNotActive;

                stickyChar.transform.GetChild(0).gameObject.SetActive(false);
                stickyChar.transform.GetChild(1).gameObject.SetActive(true);
                stickyChar.transform.GetChild(2).gameObject.SetActive(true);
                stickyChar.transform.GetChild(3).gameObject.SetActive(true);
                stickyChar.transform.GetChild(5).gameObject.SetActive(true);
                stickyChar.transform.GetChild(6).gameObject.SetActive(true);
                stickyChar.transform.GetChild(7).gameObject.SetActive(true);
                stickyChar.transform.GetChild(8).gameObject.SetActive(true);
                stickyChar.transform.GetChild(9).gameObject.SetActive(!isCharNotActive);
                stickyChar.transform.GetChild(10).gameObject.SetActive(!isCharNotActive);
                stickyChar.transform.GetChild(11).gameObject.SetActive(isCharNotActive);
                var remCharButtonGO = stickyChar.transform.GetChild(11).gameObject;
                remCharButtonGO.SetActive(isCharNotActive);

                if (isCharNotActive) // only add listener if active
                {
                    stickyChar.transform.GetChild(4).gameObject.SetActive(false);
                    Button remCharButton = remCharButtonGO.GetComponent<Button>();
                    if (remCharButton != null)
                    {
                        remCharButton.onClick.RemoveAllListeners();
                        remCharButton.onClick.AddListener(delegate { partyConfigs.RemoveCharacterFromParty(remCharButton); });
                    }
                }
                else
                {
                    //Remove listeners if inactive
                    Button remCharButton = remCharButtonGO.GetComponent<Button>();
                    if (remCharButton != null)
                    {
                        remCharButton.onClick.RemoveAllListeners();
                    }
                }

                stickyChar.transform.GetChild(2).GetComponent<TextMeshProUGUI>().text = character.name;
                stickyChar.transform.GetChild(3).GetComponent<TextMeshProUGUI>().text = "Lv: " + character.level.ToString();
                stickyChar.transform.GetChild(5).GetChild(0).GetComponent<Image>().fillAmount = (character.maxHP == 0) ? 1 : (float)character.hP / character.maxHP;
                stickyChar.transform.GetChild(5).GetChild(1).GetChild(0).GetComponent<TextMeshProUGUI>().text = character.hP.ToString();
                Types type1 = character.skills.GetHighestSpDef();
                stickyChar.transform.GetChild(6).GetChild(0).GetComponent<Image>().sprite = GetTypeSprite(type1);
                Types type2 = character.skills.GetHighestSpAtk();
                stickyChar.transform.GetChild(7).GetChild(0).GetComponent<Image>().sprite = GetTypeSprite(type2);
                Types type3 = character.skills.GetLowestSpDef();
                stickyChar.transform.GetChild(8).GetChild(0).GetComponent<Image>().sprite = GetTypeSprite(type3);

                if (isCharDead)
                {
                    stickyChar.transform.GetChild(9).gameObject.SetActive(true);
                    stickyChar.transform.GetChild(10).gameObject.SetActive(true);
                    stickyChar.transform.GetChild(9).GetComponent<Image>().color = new Color(1f, 0f, 0f, 0.4f);
                    stickyChar.transform.GetChild(10).GetComponent<TextMeshProUGUI>().text = "DEAD";
                }
                else
                {
                    stickyChar.transform.GetChild(9).GetComponent<Image>().color = new Color(0f, 1f, 0f, 0.4f);
                    stickyChar.transform.GetChild(10).GetComponent<TextMeshProUGUI>().text = "ACTIVE";
                }
            }
            else // otherwise it hide the values
            {
                stickyChar.GetComponent<Button>().interactable = true;
                stickyChar.transform.GetChild(0).gameObject.SetActive(true);
                stickyChar.transform.GetChild(1).gameObject.SetActive(false);
                stickyChar.transform.GetChild(2).gameObject.SetActive(false);
                stickyChar.transform.GetChild(3).gameObject.SetActive(false);
                stickyChar.transform.GetChild(5).gameObject.SetActive(false);
                stickyChar.transform.GetChild(6).gameObject.SetActive(false);
                stickyChar.transform.GetChild(7).gameObject.SetActive(false);
                stickyChar.transform.GetChild(8).gameObject.SetActive(false);
                stickyChar.transform.GetChild(9).gameObject.SetActive(false);
                stickyChar.transform.GetChild(10).gameObject.SetActive(false);
                stickyChar.transform.GetChild(11).gameObject.SetActive(false);
            }
        }

    }

    void LoadAllTypeSprites()
    {
        foreach (Types type in System.Enum.GetValues(typeof(Types)))
        {
            if (!typeSpriteCache.ContainsKey(type))
            {
                Sprite sprite = Resources.Load<Sprite>($"Sprites/BattleEffects/{type}");
                if (sprite != null)
                {
                    typeSpriteCache[type] = sprite;
                }
                else
                {
                    Debug.LogWarning($"Missing sprite for type: {type}");
                }
            }
        }
    }

    Sprite GetTypeSprite(Types type)
    {
        if (typeSpriteCache.TryGetValue(type, out Sprite sprite))
        {
            return sprite;
        }
        else
        {
            Debug.LogWarning($"Sprite not found for type: {type}");
            return null;
        }
    }
}
