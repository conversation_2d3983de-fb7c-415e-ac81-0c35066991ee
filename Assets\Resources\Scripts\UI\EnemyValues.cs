using UnityEngine;
using TMPro;
using UnityEngine.UI;
using System.Linq;
using System.Collections;

public class EnemyValues : MonoBehaviour
{
    BattleCharacter character = null; // the enemy character to show the values

    ScrollRect scrollRect;

    ConfigsHandler configsHandler;

    // References to the UI elements
    public GameObject classType, level, healthBar, strength, magic, fire, venom, possession, electricity, acid, frost, charName, rarity,
                    charm, confusion, curse, paralysis, sleep,
                      knoLabel, lckLabel, spdLabel, evsLabel, prcLabel, critLabel;

    public Image pageIndicator1;
    public Image pageIndicator2;

    Image healthBarImg;
    TextMeshProUGUI healthAmount, classTypeText;

    private void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();

        scrollRect = gameObject.GetComponent<ScrollRect>();
        scrollRect.onValueChanged.AddListener(_ =>
        {
            HandlePage(); // Updates the page indicator based on the scroll position
        });

        pageIndicator1.color = Color.white;
        pageIndicator2.color = new Color(1f, 1f, 1f, 0.3f);

        classTypeText = classType.GetComponent<TextMeshProUGUI>();
        healthBarImg = healthBar.transform.GetChild(0).GetComponent<Image>();
        healthAmount = healthBar.transform.GetChild(1).GetChild(0).GetComponent<TextMeshProUGUI>();
    }

    void Update()
    {
        // If the player touches the screen, the values are hidden
        //if (Input.touchCount > 0 && Input.GetTouch(0).phase == TouchPhase.Ended) gameObject.SetActive(false);

        if (character != null) // if the enemy isn't null it updates the values for the values in the enemy
        {
            classTypeText.text = ClassType();

            level.GetComponent<TextMeshProUGUI>().text = character.level.ToString();
            charName.GetComponent<TextMeshProUGUI>().text = character.name;
            rarity.GetComponent<TextMeshProUGUI>().text = character.rarity;

            healthBarImg.fillAmount = character.hP / (float)character.maxHP;

            healthBarImg.color =
                Tools.InterpolateColor(
                    new(178f / 255f, 250f / 255f, 67f / 255f),
                    Color.red,
                    healthBarImg.fillAmount,
                    5f);

            healthAmount.text = !character.isEnemy || (character.isEnemy && configsHandler.ShowHP.GetComponent<Toggle>().isOn) ? character.hP.ToString() : "???";

            UpdateSkillDisplay(strength, character.skills.GetValues(Types.Strength), Types.Strength);
            UpdateSkillDisplay(magic, character.skills.GetValues(Types.Magic), Types.Magic);
            UpdateSkillDisplay(fire, character.skills.GetValues(Types.Fire), Types.Fire);
            UpdateSkillDisplay(venom, character.skills.GetValues(Types.Venom), Types.Venom);
            UpdateSkillDisplay(possession, character.skills.GetValues(Types.Possession), Types.Possession);
            UpdateSkillDisplay(electricity, character.skills.GetValues(Types.Electricity), Types.Electricity);
            UpdateSkillDisplay(acid, character.skills.GetValues(Types.Acid), Types.Acid);
            UpdateSkillDisplay(frost, character.skills.GetValues(Types.Frost), Types.Frost);


            UpdateAilmentUI(charm, character.ailDefs.GetCharm());
            UpdateAilmentUI(confusion, character.ailDefs.GetConfusion());
            UpdateAilmentUI(curse, character.ailDefs.GetCurse());
            UpdateAilmentUI(paralysis, character.ailDefs.GetParalysis());
            UpdateAilmentUI(sleep, character.ailDefs.GetSleep());

            UpdateMods();
        }
    }
    

    void UpdateSkillDisplay(GameObject skillObj, SkillValues skill, Types type)
    {
        if (character.isEnemy && configsHandler.enemyAttacksType[type] == 0 && configsHandler.playerAttacksType[type] == 0) // If enemy hasnt used this type of attack and the player hasnt attacked it displays "?"
        {
            skillObj.transform.GetChild(0).gameObject.SetActive(true);
            skillObj.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = "?";
            skillObj.transform.GetChild(1).gameObject.SetActive(false);
            skillObj.transform.GetChild(2).GetComponent<TextMeshProUGUI>().text = "?";
            return;
        }

        string atkValue = skill.spAtk?.ToString();
        string defValue = skill.spDef?.ToString();

        if (defValue == "Nulo" || defValue == "Repelir" || defValue == "Absorver")
        {
            skillObj.transform.GetChild(0).gameObject.SetActive(false);
            skillObj.transform.GetChild(1).gameObject.SetActive(true);
            skillObj.transform.GetChild(1).GetComponent<Image>().sprite = Resources.Load<Sprite>("Sprites/UI/" + defValue);
        }
        else
        {
            skillObj.transform.GetChild(0).gameObject.SetActive(true);
            skillObj.transform.GetChild(1).gameObject.SetActive(false);
            skillObj.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = defValue;
        }

        skillObj.transform.GetChild(2).GetComponent<TextMeshProUGUI>().text =
            string.IsNullOrEmpty(atkValue) || atkValue == "0" ? "━" : atkValue;

        skillObj.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text =
            string.IsNullOrEmpty(defValue) || defValue == "0" ? "━" : defValue;
    }

    string ClassType()
    {
        string classe = character.classe;
        return classe;
    }

    // Updates the character to show the values
    public void UpdateCharacter(BattleCharacter character) => this.character = character;

    void HandlePage()
    {
        if (scrollRect.horizontalNormalizedPosition < 0.5f)
        {
            pageIndicator1.color = Color.white;
            pageIndicator2.color = new Color(1f, 1f, 1f, 0.3f);
        }
        else
        {
            pageIndicator1.color = new Color(1f, 1f, 1f, 0.3f);
            pageIndicator2.color = Color.white;
        }
    }

    void UpdateAilmentUI(GameObject ailment, string value)
    {
        GameObject iconA = ailment.transform.GetChild(0).gameObject;
        GameObject iconB = ailment.transform.GetChild(1).gameObject;

        string spritePath = value switch
        {
            "Fraco" => "Sprites/UI/Fraco",
            "Resiste" => "Sprites/UI/Resiste",
            "Imune" => "Sprites/UI/Nulo",
            _ => null
        };

        if (value == "Normal" || spritePath == null)
        {
            iconA.SetActive(false);
            iconB.SetActive(true);
        }
        else
        {
            iconB.SetActive(false);
            iconA.SetActive(true);
            Image imageComponent = iconA.GetComponent<Image>();
            imageComponent.sprite = Resources.Load<Sprite>(spritePath);
        }
    }

    void UpdateMods()
    {
        knoLabel.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = character.mods.GetKnowledge().ToString();
        lckLabel.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = character.mods.GetLuck().ToString();
        spdLabel.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = character.mods.GetSpeed().ToString();
        evsLabel.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = character.mods.GetEvasion().ToString() + "%";
        prcLabel.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = character.mods.GetPrecision().ToString() + "%";
        critLabel.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = character.mods.GetCriticalChance().ToString() + "%";
    }
}
