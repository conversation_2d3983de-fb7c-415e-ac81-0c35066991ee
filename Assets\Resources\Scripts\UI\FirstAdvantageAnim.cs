using TMPro;
using UnityEngine;
using DG.Tweening;

public class FirstAdvantageAnim : MonoBehaviour
{
    ConfigsHandler configsHandler;
    TextMeshProUGUI text;
    RectTransform rectTransform;

    [Header("Animation Settings")]
    [SerializeField] private float punchDuration = 0.15f;
    [SerializeField] private float overshootCorrection = 0.05f;
    [SerializeField] private float centerHoldTime = 0.2f;
    [SerializeField] private float returnSpeedMultiplier = 0.5f;
    [SerializeField] private float overshootDistance = 15f; // Percentage past center (10-20%)

    private Vector2 originalPosition;
    private bool isAnimating = false;

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script
        text = GetComponent<TextMeshProUGUI>();
        rectTransform = GetComponent<RectTransform>();

        // Store the original position
        originalPosition = rectTransform.anchoredPosition;
    }

    // Update is called once per frame
    void Update()
    {
        WichTurn();
    }

    void WichTurn()
    {
        if (configsHandler.playerTurn)
        {
            text.text = "Player Advantage";
            text.fontMaterial = Resources.Load<Material>("Materials/FOT-NewRodin Pro Player");
        }
        else
        {
            text.text = "Enemy Advantage";
            text.fontMaterial = Resources.Load<Material>("Materials/FOT-NewRodin Pro Enemy");
        }
    }

    /// <summary>
    /// Animates the GameObject with a punch-to-center overshoot pattern and aggressive snap-back
    /// </summary>
    public void PlayCenterAnimation()
    {
        if (isAnimating) return; // Prevent multiple animations from running simultaneously

        isAnimating = true;

        // Kill any existing tweens on this transform to prevent conflicts
        DOTween.Kill(rectTransform);

        // Calculate positions
        Vector2 centerPosition = Vector2.zero;
        Vector2 directionToCenter = (centerPosition - originalPosition).normalized;
        Vector2 overshootPosition = centerPosition + (directionToCenter * overshootDistance);

        // Create a sequence for the complete animation
        Sequence animSequence = DOTween.Sequence();

        // PHASE 1: Rapid acceleration to overshoot position (punch effect)
        animSequence.Append(rectTransform.DOAnchorPos(overshootPosition, punchDuration).SetEase(Ease.InQuart));

        // PHASE 2: Quick snap back to exact center position (correction)
        animSequence.Append(rectTransform.DOAnchorPos(centerPosition, overshootCorrection).SetEase(Ease.OutQuart));

        // PHASE 3: Hold at center for specified duration
        animSequence.AppendInterval(centerHoldTime);

        // PHASE 4: Aggressive snap back to original position (50%+ faster)
        float returnDuration = punchDuration * returnSpeedMultiplier;
        animSequence.Append(rectTransform.DOAnchorPos(originalPosition, returnDuration).SetEase(Ease.InExpo));

        // Set callback when animation completes
        animSequence.OnComplete(() =>
        {
            isAnimating = false;
        });

        // Set the target for the sequence
        animSequence.SetTarget(rectTransform);
    }

    /// <summary>
    /// Stops the current animation and returns to original position
    /// </summary>
    public void StopAnimation()
    {
        DOTween.Kill(rectTransform);
        rectTransform.anchoredPosition = originalPosition;
        isAnimating = false;
    }

    /// <summary>
    /// Updates the original position (useful if the UI layout changes)
    /// </summary>
    public void UpdateOriginalPosition()
    {
        if (!isAnimating)
        {
            originalPosition = rectTransform.anchoredPosition;
        }
    }
}
