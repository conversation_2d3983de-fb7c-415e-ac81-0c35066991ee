using System.Collections;
using DG.Tweening;
using TMPro;
using UnityEngine;

public class WarningLabel : MonoBehaviour
{
    public static WarningLabel Instance { get; private set; }
    public GameObject warningLabel;
    TextMeshProUGUI warningLabelText;
    private CanvasGroup canvasGroup;
    private Coroutine currentMessageCoroutine;

    [SerializeField] private float fadeDuration = 0.3f;
    [SerializeField] private float visibleDuration = 2f;

    void Awake()
    {
        // Singleton setup
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }
        Instance = this;
    }

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        warningLabelText = warningLabel.GetComponentInChildren<TextMeshProUGUI>();
        canvasGroup = warningLabel.GetComponent<CanvasGroup>();

        if (canvasGroup == null)
            canvasGroup = warningLabel.AddComponent<CanvasGroup>();

        canvasGroup.alpha = 0f;
        warningLabel.SetActive(false);
    }

    // Update is called once per frame
    void Update()
    {

    }

    public void ShowMessage(string message)
    {
        // Stop previous message coroutine if one is running
        if (currentMessageCoroutine != null)
        {
            StopCoroutine(currentMessageCoroutine);
        }

        currentMessageCoroutine = StartCoroutine(Message(message));
    }

    private IEnumerator Message(string message)
    {
        warningLabelText.text = message;
        warningLabel.SetActive(true);
        canvasGroup.alpha = 0f;

        // Fade in
        canvasGroup.DOFade(1f, fadeDuration);
        yield return new WaitForSeconds(fadeDuration + visibleDuration);

        // Fade out
        canvasGroup.DOFade(0f, fadeDuration);
        yield return new WaitForSeconds(fadeDuration);

        warningLabel.SetActive(false);
    }
}
