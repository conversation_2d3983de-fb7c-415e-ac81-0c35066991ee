using System.Collections;
using TMPro;
using Unity.Burst.Intrinsics;
using UnityEngine;

public class Score : MonoBehaviour
{
    ConfigsHandler configsHandler;
    GridManager gridManager; // Get the ConfigsHandler script

    public GameObject attacksGameObject; // The GameObject of the score
    public GameObject comboGameObject; // The GameObject of the combo

    private TextMeshProUGUI attacksText; // The TextMeshProUGUI of the score
    private TextMeshProUGUI comboText; // The TextMeshProUGUI of the combo


    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script
        gridManager = GameObject.Find("GridManager").GetComponent<GridManager>(); // Get the ConfigsHandler script

        if (attacksGameObject != null && comboGameObject != null)
        {
            attacksText = attacksGameObject.GetComponent<TextMeshProUGUI>(); // Get the TextMeshProUGUI of the score
            comboText = comboGameObject.GetComponent<TextMeshProUGUI>(); // Get the TextMeshProUGUI of the combo
        }
    }

    void Update()
    {
        attacksText.text = "Attacks: " + gridManager.attacks;
        if (gridManager.enemy != null && gridManager.enemy.missCheck) comboText.text = "Combos: " + Mathf.Max(gridManager.combo, 0); // Reset combo if attack miss
        else comboText.text = "Combos: " + Mathf.Max(gridManager.attacks - 1, 0); // It's done like this because combo updates too soon (before the attack starts, user will know that the combo is coming before it comes)
    }

}
