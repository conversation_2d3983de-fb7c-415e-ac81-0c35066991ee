using TMPro;
using UnityEngine;

public class EditReductPerPiece : MonoBehaviour
{
    private ConfigsHandler configsHandler; // Reference to the ConfigsHandler
    private TMP_InputField text;

    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script

        text = GetComponent<TMP_InputField>(); // Get the TMP_InputField component

        text.text = configsHandler.ReductionPerCombo.ToString(); // Add the listener to the TMP_InputField to edit the value

        text.onEndEdit.AddListener(EditValue); // Add the listener to the TMP_InputField to edit the value
    }

    void EditValue(string arg) // Edit the value of the energy timer
    {
        if (int.Parse(arg) < 0) arg = "1"; // If the value is less than 0 set it to 1

        text.text = arg;

        configsHandler.ReductionPerCombo = float.Parse(arg); // Set the energy timer to the new value
        configsHandler.SaveValues(); // Save the value
    }
}