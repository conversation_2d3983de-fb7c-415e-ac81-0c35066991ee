using TMPro;
using UnityEngine.UI;
using UnityEngine;
using System.Collections.Generic;

public class SkillsValueDisplay : MonoBehaviour
{

    readonly List<SkillValuesInputs> inputs = new(); // Inputs

    CharConfUI confUI; // Config UI

    void Start()
    {
        // gets the GameObject of the config canvas
        GameObject configCanvas = GameObject.Find("ConfigCanvas");

        // creates the objects that contain the inputs for UI
        for (int i = 0; i < System.Enum.GetValues(typeof(Types)).Length; i++)
        {
            // gets the prefab if the Skill Values
            GameObject skillPrefab = Resources.Load<GameObject>("Prefabs/SkillValuesInputs");

            // instantiates the prefab
            GameObject skill = Instantiate(skillPrefab, transform);
            skill.name = ((Types)i).ToString(); // sets its name to the type

            // loads the sprite
            skill.GetComponent<Image>().sprite = Resources.Load<Sprite>("Sprites/UI/" + ((Types)i).ToString());

            // sets the type of the skill and the first 2 letters
            skill.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = ((Types)i).ToString();
            skill.transform.GetChild(1).GetComponent<TextMeshProUGUI>().text = ((Types)i).ToString()[..2];

            // sets the offset of the skill
            skill.transform.position -= new Vector3(0, 80 * transform.localScale.x * i, 0) * configCanvas.transform.localScale.x;

            // creates the inputs
            SkillValuesInputs input = new((Types)i, skill.transform.GetChild(2).GetComponent<TMP_InputField>(), skill.transform.GetChild(3).GetComponent<TMP_InputField>());

            // adds the input to the list
            inputs.Add(input);
        }

        foreach (var input in inputs) // set the listeners for the Special Defense and Special Attack inputs
        {
            input.spDef.onEndEdit.AddListener(delegate { confUI.ChangeSPDefAndAtk(input.type, input.spDef.text, input.spAtk.text); });
            input.spAtk.onEndEdit.AddListener(delegate { confUI.ChangeSPDefAndAtk(input.type, input.spDef.text, input.spAtk.text); });
        }

        configCanvas.GetComponent<Canvas>().enabled = false;
    }

    void PasteValues()
    {
        if (GameObject.Find("SkillsValues") == null) return; // returns if the skills values is disable

        string clipboardText = GUIUtility.systemCopyBuffer; // gets the text in the clipboard
        if (clipboardText.Length > 0)
        {
            // splits the text
            string[] values = clipboardText.Split('\n');

            // makes a list of all the types
            var types = System.Enum.GetValues(typeof(Types));

            if (values[0].Split("\t").Length < 2) return; // returns if there are not enough values

            if (values[0].Split("\t").Length > 2) return; // returns if there are too many values

            for (int i = 0; i < values.Length - 1; i++) // goes through all the values in the clipboard
            {
                if (i >= types.Length) break; // returns if the clipboard at the index is bigger than the number of types

                string[] stats = values[i].Split('\t'); // splits the values

                confUI.ChangeSPDefAndAtk((Types)i, int.Parse(stats[0]), int.Parse(stats[1])); // changes the special defense and special attack
            }
        }
    }

    void Randomize() => confUI.RandomizeSpDefAndAtk();

public void SetValues(BattleCharacter character, CharConfUI confUI) // updates the values on the UI
{
    this.confUI = confUI;

    foreach (var input in inputs)
    {
        string spDef = character.skills.GetValues(input.type).spDef;
        string spAtk = character.skills.GetValues(input.type).spAtk;

        input.spDef.text = string.IsNullOrEmpty(spDef) ? "Inexistente" : (spDef == "0" ? "━" : spDef);
        input.spAtk.text = string.IsNullOrEmpty(spAtk) ? "Inexistente" : (spAtk == "0" ? "━" : spAtk);
    }
}
}


public class SkillValuesInputs
{
    public TMP_InputField spDef, spAtk; // special defense and special attack input fields
    public Types type;

    /// <summary>
    /// Initializes a new instance of the <see cref="SkillValuesInputs"/> class, this is for UI handling only.
    /// </summary>
    /// <param name="type">the type of the skill</param>
    /// <param name="spDef">the special defense input field</param>
    /// <param name="spAtk">the special attack input field</param>
    public SkillValuesInputs(Types type, TMP_InputField spDef, TMP_InputField spAtk)
    {
        this.type = type;
        this.spDef = spDef;
        this.spAtk = spAtk;
    }
}
