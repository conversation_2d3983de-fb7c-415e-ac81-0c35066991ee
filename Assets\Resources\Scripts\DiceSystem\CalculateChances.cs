using System.Collections;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class CalculateChances : MonoBehaviour
{
    ConfigsHandler configsHandler;
    GridManager gridManager;

    GetMeshFaceUp dice;

    public GameObject Dice;

    public float frames = 10;

    [HideInInspector]
    public int rollAttempts = 0, roll = -1;
    int bonus = 0;
    public int[] rollPrices = new int[] { 0, 2, 3, 5, 8, 0 };

    bool canCheckSucces = true;

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        dice = GameObject.Find("Dice1").GetComponent<GetMeshFaceUp>();

        roll = -1;

        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();

        gridManager = GameObject.Find("GridManager").GetComponent<GridManager>();

        transform.GetChild(5).GetComponent<Button>().onClick.AddListener(RollDice);
        transform.GetChild(6).GetComponent<Button>().onClick.AddListener(GetMoreRolls);
        transform.GetChild(7).GetComponent<Button>().onClick.AddListener(delegate { new ReloadGame(); });

        foreach (var bunusButton in transform.GetChild(8).GetComponentsInChildren<Button>()) bunusButton.onClick.AddListener(delegate { SetBonus(int.Parse(bunusButton.name)); });
        gameObject.SetActive(false);
    }

    void Update()
    {
        transform.GetChild(2).GetComponent<TextMeshProUGUI>().text = CalculateDC().ToString();
        transform.GetChild(3).GetComponent<TextMeshProUGUI>().text = (100 / 20 * (21 + bonus - Mathf.Min(CalculateDC(), 21 + bonus))) + "%";
        transform.GetChild(5).GetChild(0).GetComponent<TextMeshProUGUI>().text = rollPrices[rollAttempts].ToString();
        transform.GetChild(6).GetChild(0).GetComponent<TextMeshProUGUI>().text = (rollPrices[rollAttempts] + 2 * Mathf.Min(5,rollAttempts + 1)).ToString();
        transform.GetChild(8).GetChild(0).GetComponent<TextMeshProUGUI>().text = "Total Bonus +" + bonus;

        transform.GetChild(5).gameObject.SetActive(rollAttempts != 5);

        transform.GetChild(6).gameObject.SetActive(rollAttempts == 5);

        if (dice.diceSide + bonus >= CalculateDC() && canCheckSucces && rollAttempts != 0 && dice.CanRollTheDice()) StartCoroutine(Succecess());
    }

    IEnumerator Succecess()
    {
        canCheckSucces = false;
        for (int i = 0; i < frames; i++) yield return new WaitForSeconds(1 / 60);

        configsHandler.HealDeadCharacters(5 * dice.diceSide);

        roll = -1; rollAttempts = 0; bonus = 0;

        configsHandler.win = false;

        if (Dice.activeSelf)
            Dice.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = "";

        transform.parent.GetChild(0).gameObject.SetActive(true);
        canCheckSucces = true;
        configsHandler.FailPopup.SetActive(false);
        gridManager.ReviveRunes();
        gameObject.SetActive(false);

    }


    float CalculateIDB()
    {
        float bl = configsHandler.CalculateAverageEnemyLevel(), pl = configsHandler.CalculatePartyAverageLevel();

        float IDB = Mathf.Max(0, Mathf.Min(bl - pl, 30) - configsHandler.IDBOffset);

        return IDB;
    }

    int CalculateDC()
    {
        float IDB = CalculateIDB();
        float BL = configsHandler.CalculateAverageLevel();

        int DC = (int)Mathf.Round(Mathf.Min(30, IDB + (BL / 100 * ((30 - IDB) / 2))));

        return DC;
    }

    void RollDice()
    {
        if (rollAttempts < 5 && dice.CanRollTheDice() && !(dice.diceSide + bonus >= CalculateDC() && canCheckSucces && rollAttempts != 0))
        {
            rollAttempts++;

            dice.RollTheDice();

            roll = Random.Range(1, 21);
            if (Dice.activeSelf)
                Dice.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = roll.ToString();
        }
    }

    void GetMoreRolls()
    {
        RollDice();
        rollAttempts = 0;
    }

    void SetBonus(int amount)
    {
        roll = -11;
        bonus = amount;
    }
}
