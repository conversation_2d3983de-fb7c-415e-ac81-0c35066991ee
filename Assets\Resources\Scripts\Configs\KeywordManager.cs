using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// Singleton manager for keyword localization system
/// Provides global access to localized text strings through key-based lookup
/// Loads keywords from generalInfo.json and handles graceful fallbacks for missing keys
/// 
/// Usage:
/// - KeywordManager.GetWord("GENERIC_ACCEPT") returns "Confirmar"
/// - KeywordManager.GetWordOrDefault("MISSING_KEY", "Default Text") returns "Default Text"
/// - KeywordManager.<PERSON><PERSON><PERSON>("GENERIC_ACCEPT") returns true/false
/// 
/// The system is read-only from game code perspective - all modifications must come through import process
/// </summary>
public class KeywordManager : MonoBehaviour
{
    #region Singleton Implementation
    private static KeywordManager _instance;
    
    /// <summary>
    /// Singleton instance accessor
    /// Automatically creates instance if none exists
    /// </summary>
    public static KeywordManager Instance
    {
        get
        {
            if (_instance == null)
            {
                // Try to find existing instance in scene
                _instance = FindObjectOfType<KeywordManager>();
                
                if (_instance == null)
                {
                    // Create new GameObject with KeywordManager component
                    GameObject keywordManagerObject = new GameObject("KeywordManager");
                    _instance = keywordManagerObject.AddComponent<KeywordManager>();
                    DontDestroyOnLoad(keywordManagerObject);
                }
            }
            return _instance;
        }
    }
    #endregion

    #region Private Fields
    /// <summary>
    /// Dictionary for fast keyword lookup by key
    /// Key: string key (e.g., "GENERIC_ACCEPT")
    /// Value: JsonKeyword object containing all keyword data
    /// </summary>
    private Dictionary<string, JsonKeyword> keywordLookup = new Dictionary<string, JsonKeyword>();
    
    /// <summary>
    /// Flag to track if keywords have been loaded successfully
    /// </summary>
    private bool isLoaded = false;
    
    /// <summary>
    /// Cached reference to loaded keyword data for debugging and inspection
    /// </summary>
    private JsonGeneralInfo generalInfo;
    #endregion

    #region Unity Lifecycle
    private void Awake()
    {
        // Ensure singleton pattern
        if (_instance == null)
        {
            _instance = this;
            DontDestroyOnLoad(gameObject);
            LoadKeywords();
        }
        else if (_instance != this)
        {
            Destroy(gameObject);
        }
    }
    #endregion

    #region Public Static Methods
    /// <summary>
    /// Gets the localized word for the specified key
    /// Returns the key itself if not found (graceful fallback)
    /// </summary>
    /// <param name="key">The keyword key to look up (e.g., "GENERIC_ACCEPT")</param>
    /// <returns>Localized word or the key itself if not found</returns>
    public static string GetWord(string key)
    {
        return Instance.GetWordInternal(key);
    }

    /// <summary>
    /// Gets the localized word for the specified key with a custom default
    /// Returns the provided default value if key is not found
    /// </summary>
    /// <param name="key">The keyword key to look up</param>
    /// <param name="defaultValue">Default value to return if key not found</param>
    /// <returns>Localized word or default value if not found</returns>
    public static string GetWordOrDefault(string key, string defaultValue)
    {
        return Instance.GetWordOrDefaultInternal(key, defaultValue);
    }

    /// <summary>
    /// Checks if a keyword key exists in the system
    /// </summary>
    /// <param name="key">The keyword key to check</param>
    /// <returns>True if key exists, false otherwise</returns>
    public static bool HasKey(string key)
    {
        return Instance.HasKeyInternal(key);
    }

    /// <summary>
    /// Gets the complete keyword object for advanced usage
    /// Returns null if key not found
    /// </summary>
    /// <param name="key">The keyword key to look up</param>
    /// <returns>JsonKeyword object or null if not found</returns>
    public static JsonKeyword GetKeyword(string key)
    {
        return Instance.GetKeywordInternal(key);
    }

    /// <summary>
    /// Gets all available keyword keys for debugging or UI population
    /// </summary>
    /// <returns>Array of all available keyword keys</returns>
    public static string[] GetAllKeys()
    {
        return Instance.GetAllKeysInternal();
    }

    /// <summary>
    /// Forces reload of keywords from generalInfo.json
    /// Useful after import operations or for debugging
    /// </summary>
    public static void ReloadKeywords()
    {
        Instance.LoadKeywords();
    }

    /// <summary>
    /// Gets the total number of loaded keywords
    /// </summary>
    /// <returns>Number of keywords currently loaded</returns>
    public static int GetKeywordCount()
    {
        return Instance.keywordLookup.Count;
    }
    #endregion

    #region Private Implementation Methods
    /// <summary>
    /// Internal method to load keywords from generalInfo.json
    /// Called automatically on initialization and can be called manually to reload
    /// </summary>
    private void LoadKeywords()
    {
        try
        {
            // Clear existing data
            keywordLookup.Clear();
            isLoaded = false;

            // Load from JSON using existing JsonSaveHelper pattern
            if (JsonSaveHelper.FileExists("generalInfo.json"))
            {
                generalInfo = JsonSaveHelper.LoadFromJson<JsonGeneralInfo>("generalInfo.json");
                
                if (generalInfo != null && generalInfo.keywordPkg != null)
                {
                    // Build lookup dictionary
                    foreach (var keyword in generalInfo.keywordPkg)
                    {
                        if (keyword != null && !string.IsNullOrEmpty(keyword.key))
                        {
                            // Use key as dictionary key for fast lookup
                            keywordLookup[keyword.key] = keyword;
                        }
                    }
                    
                    isLoaded = true;
                    Debug.Log($"[KeywordManager] ✅ Loaded {keywordLookup.Count} keywords from generalInfo.json");
                }
                else
                {
                    Debug.LogWarning("[KeywordManager] ⚠️ generalInfo.json exists but contains no keyword data");
                }
            }
            else
            {
                Debug.LogWarning("[KeywordManager] ⚠️ generalInfo.json not found - keywords not available");
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"[KeywordManager] ❌ Failed to load keywords: {ex.Message}");
            isLoaded = false;
        }
    }

    private string GetWordInternal(string key)
    {
        if (string.IsNullOrEmpty(key))
            return "";

        if (keywordLookup.TryGetValue(key, out JsonKeyword keyword))
        {
            return keyword.word ?? key;
        }

        // Graceful fallback - return the key itself
        Debug.LogWarning($"[KeywordManager] ⚠️ Keyword key '{key}' not found, returning key as fallback");
        return key;
    }

    private string GetWordOrDefaultInternal(string key, string defaultValue)
    {
        if (string.IsNullOrEmpty(key))
            return defaultValue ?? "";

        if (keywordLookup.TryGetValue(key, out JsonKeyword keyword))
        {
            return keyword.word ?? defaultValue;
        }

        return defaultValue ?? "";
    }

    private bool HasKeyInternal(string key)
    {
        return !string.IsNullOrEmpty(key) && keywordLookup.ContainsKey(key);
    }

    private JsonKeyword GetKeywordInternal(string key)
    {
        if (string.IsNullOrEmpty(key))
            return null;

        keywordLookup.TryGetValue(key, out JsonKeyword keyword);
        return keyword;
    }

    private string[] GetAllKeysInternal()
    {
        return keywordLookup.Keys.ToArray();
    }
    #endregion

    #region Debug and Utility Methods
    /// <summary>
    /// Debug method to print all loaded keywords to console
    /// Useful for development and troubleshooting
    /// </summary>
    [ContextMenu("Debug Print All Keywords")]
    public void DebugPrintAllKeywords()
    {
        if (!isLoaded)
        {
            Debug.Log("[KeywordManager] 🔍 No keywords loaded");
            return;
        }

        Debug.Log($"[KeywordManager] 🔍 Loaded Keywords ({keywordLookup.Count} total):");
        foreach (var kvp in keywordLookup)
        {
            var keyword = kvp.Value;
            string tags = keyword.keywordTags != null ? string.Join(", ", keyword.keywordTags) : "none";
            Debug.Log($"  Key: '{keyword.key}' | Word: '{keyword.word}' | ID: '{keyword.id}' | Tags: [{tags}]");
        }
    }

    /// <summary>
    /// Gets loading status for debugging
    /// </summary>
    /// <returns>True if keywords are loaded successfully</returns>
    public bool IsLoaded()
    {
        return isLoaded;
    }
    #endregion
}
