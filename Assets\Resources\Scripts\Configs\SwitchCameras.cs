using System.Linq;
using UnityEngine;
using UnityEngine.UI;

public class SwitchCameras : MonoBehaviour
{
    public Camera mainCam;
    public Camera configCam;

    public Canvas GamePlayCanvas;
    public Canvas ConfigsCanvas;
    ConfigsHandler configsHandler;
    public PartyConfigs partyConfigs;
    public CharResContainer charResContainer;

    private bool partyUIRefreshed = false;

    private void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();
        GetComponent<Button>().onClick.AddListener(Switch);
    }

    private void Update()
    {
        if (configCam.enabled) // Refresh Party UI every time
        {
            if (!partyUIRefreshed && partyConfigs != null)
            {
                partyConfigs.RefreshPartyUI();
                charResContainer.RefreshCharResUI();
                partyUIRefreshed = true; // Mark as refreshed
            }
        }
        else
        {
            // Reset the flag when configCam is turned off
            partyUIRefreshed = false;
        }
    }

    private void Switch()
    {
        // Save the values to the file
        StartCoroutine(configsHandler.LoadedValues.Save());

        // hides the values of a character
        configsHandler.StatsValues.SetActive(false);
        configsHandler.SkillsValues.SetActive(false);
        configsHandler.ModsValues.SetActive(false);

        // switches the cameras
        mainCam.enabled = !mainCam.enabled;
        configCam.enabled = !configCam.enabled;

        // switches the canvases
        GamePlayCanvas.enabled = mainCam.enabled;
        ConfigsCanvas.enabled = configCam.enabled;

        // makes all the characters UI buttons inside the configs menu not selected
        var obj = FindObjectsByType<GameObject>(FindObjectsSortMode.None).Where(go => go.name == "CharInfo");
        obj.ToList().ForEach(go => go.SetActive(false));

    }
}
