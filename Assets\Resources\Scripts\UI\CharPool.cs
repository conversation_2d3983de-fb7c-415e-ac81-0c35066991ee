using UnityEngine;
using System.Collections.Generic;

public class CharPool : MonoBehaviour
{
    public GameObject characterPrefab;
    public Transform parent;
    public int poolSize = 20; // Adjust based on visible area


    private List<GameObject> pool = new List<GameObject>();

    void Awake()
    {
        for (int i = 0; i < poolSize; i++)
        {
            var obj = Instantiate(characterPrefab, parent);
            obj.name = "Char " + i;
            obj.SetActive(false);
            pool.Add(obj);
        }
    }

    public GameObject GetPooledObject()
    {
        foreach (var obj in pool)
        {
            if (!obj.activeInHierarchy)
                return obj;
        }
        return null; // Or expand pool if needed
    }

    public void ReturnToPool(GameObject obj)
    {
        CharConfUI charConf = obj.GetComponent<CharConfUI>();

        // CRITICAL FIX: Clean up character references to prevent stale data
        charConf.CleanupForPooling();

        obj.SetActive(false);
    }
}
