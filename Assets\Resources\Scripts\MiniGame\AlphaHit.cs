using UnityEngine;
using UnityEngine.UI;


[RequireComponent(typeof(Image))]
public class AlphaHit : MonoBehaviour // Makes the cursor only interact with pixels that are not transparent
{
    private Image image;

    [Range(0f, 1f)]
    public float alphaThreshold = 0.1f; // Pixels with alpha below this won't count

    private void Awake()
    {
        image = GetComponent<Image>();
        if (image.sprite == null || image.sprite.texture == null)
        {
            Debug.LogWarning("AlphaHit: Image or texture missing.");
        }

        // Enable alpha hit test support for Unity UI
        image.alphaHitTestMinimumThreshold = alphaThreshold;
    }

    public bool IsRaycastLocationValid(Vector2 screenPoint, Camera eventCamera)
    {
        if (image.sprite == null || image.sprite.texture == null)
            return false;

        Vector2 local;
        RectTransformUtility.ScreenPointToLocalPointInRectangle(
            image.rectTransform, screenPoint, eventCamera, out local
        );

        Rect rect = image.GetPixelAdjustedRect();

        // Normalize point to 0-1
        float x = (local.x - rect.x) / rect.width;
        float y = (local.y - rect.y) / rect.height;

        // Out of bounds check
        if (x < 0 || x > 1 || y < 0 || y > 1)
            return false;

        Texture2D tex = image.sprite.texture;

        // Convert normalized coordinates to texture space
        int texX = Mathf.FloorToInt(image.sprite.rect.x + image.sprite.rect.width * x);
        int texY = Mathf.FloorToInt(image.sprite.rect.y + image.sprite.rect.height * y);

        try
        {
            Color color = tex.GetPixel(texX, texY);
            return color.a >= alphaThreshold;
        }
        catch
        {
            return false;
        }
    }
}
