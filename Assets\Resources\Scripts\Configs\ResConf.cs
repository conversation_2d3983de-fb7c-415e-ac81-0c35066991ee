using System;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class ResConf : MonoBehaviour
{
    public ConfigsHandler configsHandler;
    public CharResContainer charResContainer;
    public Button resButton;
    public Button cancelButton;
    public Button payButton;
    public Button char0;
    public Button char1;
    public Button char2;
    public Button char3;
    public GameObject resBkg;
    public GameObject resScreen;

    public TextMeshProUGUI goldQuantity;
    public TextMeshProUGUI label;

    int playerIndex;

    float multiplier;
    float revivalCost;
    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        // Add the listenners
        resButton.onClick.AddListener(Resurrect);
        cancelButton.onClick.AddListener(Cancel);
        cancelButton.onClick.AddListener(Cancel);
        payButton.onClick.AddListener(Revive);

        // Array of character buttons
        Button[] charButtons = { char0, char1, char2, char3 };

        for (int i = 0; i < charButtons.Length; i++)
        {
            int index = i; // Capture index in local variable for closure
            charButtons[i].onClick.AddListener(() => ShowResurrectScreen(index));
        }
    }

    // Update is called once per frame
    void Update()
    {

    }

    void Resurrect() // Shows background and make characters interactable
    {
        bool hasActiveChild = false;
        foreach (Transform child in charResContainer.transform) // Button only works if there are dead characters
        {
            if (child.gameObject.activeInHierarchy)
            {
                hasActiveChild = true;
                break;
            }
        }
        if (!hasActiveChild)
        {
            WarningLabel.Instance.ShowMessage("You don't have dead characters");
            return;
        }

        resButton.gameObject.SetActive(false);
        resBkg.SetActive(true);
        cancelButton.gameObject.SetActive(true);
        charResContainer.gameObject.GetComponent<CanvasGroup>().interactable = true;

    }

    void Cancel()
    {
        resButton.gameObject.SetActive(true);
        resBkg.SetActive(false);
        resScreen.SetActive(false);
        charResContainer.gameObject.GetComponent<CanvasGroup>().interactable = false;
        cancelButton.gameObject.SetActive(false);
    }

    void ShowResurrectScreen(int index)
    {
        playerIndex = index;
        resScreen.SetActive(true);

        BattleCharacter character = configsHandler.pC[index];
        switch (character.rarity) // Define cost of revive based on character rarity
        {
            case "Inferior":
                multiplier = 0.75f;
                break;
            case "Comum":
                multiplier = 1f;
                break;
            case "Raro":
                multiplier = 1.50f;
                break;
            case "Épico":
                multiplier = 2f;
                break;
            case "Lendário":
                multiplier = 3f;
                break;
            default:
                multiplier = 0.75f;
                break;
        }

        revivalCost = Mathf.Round(multiplier * (character.maxHP / 10));

        goldQuantity.text = "You have " + configsHandler.money.ToString() + " gold";

        label.text = "You must pay " + revivalCost + " Gold";

        payButton.gameObject.GetComponentInChildren<TextMeshProUGUI>().text = "Pay " + revivalCost;
    }

    void Revive() // Revive only if have enough gold
    {
        if (configsHandler.money < (int)revivalCost)
        {
            WarningLabel.Instance.ShowMessage("Not enough gold!");
            return;
        }

        configsHandler.money -= (int)revivalCost;

        configsHandler.HealSingleCharacter(100f, playerIndex);
        charResContainer.RefreshCharResUI();
        Cancel();
    }
 }
